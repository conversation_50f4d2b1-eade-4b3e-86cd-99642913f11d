import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { GoogleGenAI, Modality, Type, FunctionDeclaration, LiveServerMessage } from '@google/genai';
import { collection, addDoc, serverTimestamp, getDocs, query, orderBy, onSnapshot, doc, updateDoc, where, deleteDoc, limit, writeBatch } from 'firebase/firestore';
import { db } from './firebase/config';

import { ConversationTurn, EmailContext, EventType, HACCPEvent, Location, Vendor, Species, PurchaseOrder } from './types';
import { ConversationLog } from './components/ConversationLog';
import { StatusIndicator } from './components/StatusIndicator';
import { MicrophoneIcon, StopIcon, CalendarIcon, ClockIcon, XIcon, PhotoIcon, SparklesIcon, CameraIcon, DocumentArrowUpIcon, HamburgerIcon } from './components/IconComponents';
import { Dashboard } from './components/Dashboard';
import { createBlob, decode, decodeAudioData } from './utils/audioUtils';
import { blobToBase64, uploadImage } from './utils/imageUtils';
import { Sidebar } from './components/Sidebar';
// Inventory management dashboard
import InventoryManagement from './components/InventoryManagement';
import { ReportsView } from './components/ReportsView';
import { SettingsView } from './components/SettingsView';
import { HaccpLogsView } from './components/HaccpLogsView';
import { CalendarView } from './components/CalendarView';
import { VendorsView } from './components/VendorsView';
import { SpeciesView } from './components/SpeciesView';
import { LocationsView } from './components/LocationsView';
import { OrdersView } from './components/OrdersView';
import { EmailDraftModal } from './components/EmailDraftModal';
import { CameraModal } from './components/CameraModal';
import { ImportModal } from './components/ImportModal';
import { docToPlainObject } from './utils/firestoreUtils';
import { TemperatureView } from './components/TemperatureView';
import { LotTrackingView } from './components/LotTracking/LotTrackingView';
import { Login } from './components/Login';
import { auth } from './firebase/config';
import { onAuthStateChanged, signOut, User } from 'firebase/auth';

// Polyfill for webkitAudioContext
const AudioContext = window.AudioContext || (window as any).webkitAudioContext;

const App: React.FC = () => {
    // App State
    const [apiKeySelected, setApiKeySelected] = useState(false);
    const [user, setUser] = useState<User | null>(null);
    const [authLoading, setAuthLoading] = useState(true);
    const [currentUser, setCurrentUser] = useState('Default User');
    const [view, setView] = useState<'form' | 'dashboard' | 'calendar' | 'vendors' | 'species' | 'locations' | 'orders' | 'temperature' | 'inventory' | 'haccp' | 'reports' | 'settings' | 'lot-tracking'>('dashboard');
    const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
    const [locations, setLocations] = useState<Location[]>([]);
    const [vendors, setVendors] = useState<Vendor[]>([]);
    const [species, setSpecies] = useState<Species[]>([]);
    const [events, setEvents] = useState<HACCPEvent[]>([]);
    const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
    
    // Form & Editing State
    const [editingEventId, setEditingEventId] = useState<string | null>(null);
    const [eventType, setEventType] = useState<EventType>('receiving');
    const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
    const [time, setTime] = useState(new Date().toTimeString().substring(0, 5));
    const [batchNumber, setBatchNumber] = useState('');
    const [product, setProduct] = useState('');
    const [productForm, setProductForm] = useState('');
    const [quantity, setQuantity] = useState('');
    const [temperature, setTemperature] = useState('32.0');
    const [unitPrice, setUnitPrice] = useState('');
    const [origin, setOrigin] = useState('');
    const [supplier, setSupplier] = useState('');
    const [location, setLocation] = useState('');
    const [fromLocation, setFromLocation] = useState('');
    const [notes, setNotes] = useState('');
    const [areaCleaned, setAreaCleaned] = useState('');
    const [sanitizerUsed, setSanitizerUsed] = useState('');
    const [thermometerId, setThermometerId] = useState('');
    const [calibrationMethod, setCalibrationMethod] = useState('Ice Point');
    const [result, setResult] = useState<'pass' | 'fail'>('pass');
    const [correctiveAction, setCorrectiveAction] = useState('');
    const [employeeName, setEmployeeName] = useState('');
    const [trainingTopic, setTrainingTopic] = useState('');
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [imageDescription, setImageDescription] = useState('');

    // Voice Chat State
    const [isVoicePanelOpen, setIsVoicePanelOpen] = useState(false);
    const [conversation, setConversation] = useState<ConversationTurn[]>([]);
    const [isSessionActive, setIsSessionActive] = useState<boolean>(false);
    const [status, setStatus] = useState<string>('Idle. Press Start to begin.');

    // Modal State
    const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
    const [emailContext, setEmailContext] = useState<EmailContext | null>(null);
    const [isCameraModalOpen, setIsCameraModalOpen] = useState(false);
    const [isImportModalOpen, setIsImportModalOpen] = useState(false);

    const inventory = useMemo(() => {
        const inventoryMap: { [product: string]: number } = {};
    
        [...events].sort((a, b) => {
            const timeA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
            const timeB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
            return timeA - timeB;
        }).forEach(event => {
          if (!event.product || typeof event.quantity !== 'number') {
              return;
          }
    
          if (!inventoryMap[event.product]) {
            inventoryMap[event.product] = 0;
          }
    
          switch (event.eventType) {
            case 'receiving':
              inventoryMap[event.product] += event.quantity;
              break;
            case 'sales':
            case 'disposal':
              inventoryMap[event.product] -= event.quantity;
              break;
          }
        });
    
        Object.keys(inventoryMap).forEach(product => {
            if (inventoryMap[product] <= 0.01) {
                delete inventoryMap[product];
            }
        });
    
        return inventoryMap;
    }, [events]);

    const inventoryByLocation = useMemo(() => {
        const inventoryMap: { [locationName: string]: { [product: string]: number } } = {};

        [...events].sort((a, b) => {
            const timeA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
            const timeB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
            return timeA - timeB;
        }).forEach(event => {
            if (!event.product || typeof event.quantity !== 'number') return;
            const prod = event.product;
            const qty = event.quantity;
            
            switch (event.eventType) {
                case 'receiving':
                    if(event.location) {
                        if (!inventoryMap[event.location]) inventoryMap[event.location] = {};
                        if (!inventoryMap[event.location][prod]) inventoryMap[event.location][prod] = 0;
                        inventoryMap[event.location][prod] += qty;
                    }
                    break;
                case 'sales':
                case 'disposal':
                    if (event.location && inventoryMap[event.location]?.[prod]) {
                        inventoryMap[event.location][prod] -= qty;
                    }
                    break;
                case 'relocation':
                    if (event.fromLocation && inventoryMap[event.fromLocation]?.[prod]) {
                        inventoryMap[event.fromLocation][prod] -= qty;
                    }
                    if (event.location) { // 'location' is the 'to' location
                        if (!inventoryMap[event.location]) inventoryMap[event.location] = {};
                        if (!inventoryMap[event.location][prod]) inventoryMap[event.location][prod] = 0;
                        inventoryMap[event.location][prod] += qty;
                    }
                    break;
            }
        });
        
        // Clean up zero or negative quantities
        Object.keys(inventoryMap).forEach(loc => {
            Object.keys(inventoryMap[loc]).forEach(prod => {
                if (inventoryMap[loc][prod] <= 0.01) {
                    delete inventoryMap[loc][prod];
                }
            });
            if (Object.keys(inventoryMap[loc]).length === 0) {
                delete inventoryMap[loc];
            }
        });

        return inventoryMap;
    }, [events]);


    // Refs
    const sessionPromiseRef = useRef<Promise<any> | null>(null);
    const inputAudioContextRef = useRef<AudioContext | null>(null);
    const outputAudioContextRef = useRef<AudioContext | null>(null);
    const streamRef = useRef<MediaStream | null>(null);
    const workletNodeRef = useRef<AudioWorkletNode | null>(null);
    const mediaStreamSourceRef = useRef<MediaStreamAudioSourceNode | null>(null);
    const nextStartTimeRef = useRef<number>(0);
    const sourcesRef = useRef<Set<AudioBufferSourceNode>>(new Set());
    const currentInputTranscriptionRef = useRef('');
    const currentOutputTranscriptionRef = useRef('');
    
    // Auth state listener
    useEffect(() => {
        const unsubscribe = onAuthStateChanged(auth, (user) => {
            setUser(user);
            if (user) {
                setCurrentUser(user.displayName || user.email || 'User');
            }
            setAuthLoading(false);
        });
        return () => unsubscribe();
    }, []);

    useEffect(() => {
        const checkApiKey = async () => {
            if ((window as any).aistudio) {
                const hasKey = await (window as any).aistudio.hasSelectedApiKey();
                setApiKeySelected(hasKey);
            } else {
                setApiKeySelected(true);
            }
        };
        checkApiKey();
    }, []);

    useEffect(() => {
        const qLocations = query(collection(db, "locations"), orderBy("name"));
        const unsubLocations = onSnapshot(qLocations, (snapshot) => {
            const locationsList = snapshot.docs.map(doc => docToPlainObject<Location>(doc));
            setLocations(locationsList);
            if(locationsList.length > 0 && !location) {
                setLocation(locationsList[0].name);
            }
        });
        
        const qVendors = query(collection(db, "vendors"), orderBy("name"));
        const unsubVendors = onSnapshot(qVendors, (snapshot) => {
            const vendorsList = snapshot.docs.map(doc => docToPlainObject<Vendor>(doc));
            setVendors(vendorsList);
        });

        const qSpecies = query(collection(db, "species"), orderBy("name"));
        const unsubSpecies = onSnapshot(qSpecies, (snapshot) => {
            const speciesList = snapshot.docs.map(doc => docToPlainObject<Species>(doc));
            setSpecies(speciesList);
        });

        const qEvents = query(collection(db, "events"), orderBy("createdAt", "desc"));
        const unsubEvents = onSnapshot(qEvents, (snapshot) => {
            const eventsList = snapshot.docs.map(doc => docToPlainObject<HACCPEvent>(doc));
            setEvents(eventsList);
        });

        const qOrders = query(collection(db, "purchaseOrders"), orderBy("createdAt", "desc"));
        const unsubOrders = onSnapshot(qOrders, (snapshot) => {
            const ordersList = snapshot.docs.map(doc => docToPlainObject<PurchaseOrder>(doc));
            setPurchaseOrders(ordersList);
        });


        return () => {
            unsubLocations();
            unsubVendors();
            unsubSpecies();
            unsubEvents();
            unsubOrders();
        };
    }, []);
    

    useEffect(() => {
        if (eventType === 'receiving' && !editingEventId) {
            const getJulianDate = (eventDate: Date) => {
                const start = new Date(eventDate.getFullYear(), 0, 0);
                const diff = (eventDate.getTime() - start.getTime()) + ((start.getTimezoneOffset() - eventDate.getTimezoneOffset()) * 60 * 1000);
                const oneDay = 1000 * 60 * 60 * 24;
                const dayOfYear = Math.floor(diff / oneDay);
                const year = String(eventDate.getFullYear()).slice(-2);
                const day = String(dayOfYear).padStart(3, '0');
                return `${year}${day}`;
            };

            const speciesWords = product.trim().toLowerCase().split(/\s+/).filter(Boolean);
            const speciesCode = (speciesWords.length > 1
                ? speciesWords.map(w => w[0]).join('')
                : product.trim().substring(0, 2).toLowerCase()
            );

            const formCode = productForm ? productForm.trim().toLowerCase().charAt(0) : '';

            const vendorWords = supplier.trim().split(/\s+/).filter(Boolean);
            const vendorCode = vendorWords.length > 0 
                ? vendorWords.map((w, i) => i === 0 ? w[0].toUpperCase() : w[0].toLowerCase()).join('')
                : '';

            const dateObject = new Date(date + 'T00:00:00');
            const julianDate = getJulianDate(dateObject);

            const newBatchNumber = `${speciesCode}${formCode}${vendorCode}${julianDate}`;
            
            if (speciesCode || formCode || vendorCode) {
                setBatchNumber(newBatchNumber);
            } else {
                setBatchNumber('');
            }

        } else if (eventType !== 'inventory' && eventType !== 'receiving') {
            setBatchNumber('');
        }
    }, [eventType, editingEventId, product, productForm, supplier, date]);

    const resetFormAndEditingState = useCallback(() => {
        setEditingEventId(null);
        setEventType('receiving');
        setDate(new Date().toISOString().split('T')[0]);
        setTime(new Date().toTimeString().substring(0, 5));
        setBatchNumber('');
        setProduct('');
        setProductForm('');
        setQuantity('');
        setTemperature('32.0');
        setSupplier('');
        setLocation(locations.length > 0 ? locations[0].name : '');
        setFromLocation(locations.length > 0 ? locations[0].name : '');
        setNotes('');
        setAreaCleaned('');
        setSanitizerUsed('');
        setThermometerId('');
        setCalibrationMethod('Ice Point');
        setResult('pass');
        setCorrectiveAction('');
        setEmployeeName('');
        setTrainingTopic('');
        setImageFile(null);
        setImagePreview(null);
        setImageDescription('');
    }, [locations]);

    const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setImageFile(file);
            const previewUrl = URL.createObjectURL(file);
            setImagePreview(previewUrl);
        }
    };

    const handleImageAnalysis = async () => {
        if (!imageFile) return;
        setIsAnalyzing(true);
        setImageDescription('Analyzing image...');
        try {
            const ai = new GoogleGenAI({ apiKey: import.meta.env.VITE_GEMINI_API_KEY as string });
            const base64Data = await blobToBase64(imageFile);
            
            const imagePart = {
                inlineData: {
                    mimeType: imageFile.type,
                    data: base64Data,
                },
            };
            const textPart = {
                text: 'Analyze this image related to seafood inventory. Describe what you see, focusing on product type, quality, quantity, or any relevant details for a HACCP log. Is there anything concerning?',
            };
            const response = await ai.models.generateContent({
                model: 'gemini-2.5-flash',
                contents: { parts: [imagePart, textPart] },
            });
            
            setImageDescription(response.text);
        } catch (error: any) {
            console.error('Image analysis failed:', error);
            if (error.message?.includes('does not have permission') || error.message?.includes('not found')) {
                 setImageDescription("API Key Error. Please close this window and re-select your API key from the main screen.");
            } else {
                setImageDescription('Analysis failed. Please try again.');
            }
        } finally {
            setIsAnalyzing(false);
        }
    };

    const handleSubmit = async () => {
        // Fix: Use a more flexible type for the event data object to accommodate the
        // Firebase serverTimestamp() FieldValue, which is not a string. Also corrected
        // the logic to only set createdBy on new events.
        const eventData: { [key: string]: any } = {
            eventType,
            date,
            time,
            correctiveAction,
            notes,
            imageDescription,
            ...(editingEventId
                ? { updatedAt: serverTimestamp(), updatedBy: currentUser }
                : { createdAt: serverTimestamp(), createdBy: currentUser }),
        };

        if (imageFile) {
            try {
                const imageUrl = await uploadImage(imageFile, `events/${Date.now()}_${imageFile.name}`);
                eventData.imageUrl = imageUrl;
            } catch (error) {
                alert("Failed to upload image.");
                return;
            }
        } else if (editingEventId && imagePreview) {
             eventData.imageUrl = imagePreview;
        }

        switch (eventType) {
            case 'receiving':
            case 'sales':
            case 'disposal':
            case 're-sealing':
            case 'inventory':
                 Object.assign(eventData, {
                    product, productForm,
                    quantity: parseFloat(quantity) || undefined,
                    unit: 'lbs',
                    batchNumber: batchNumber || undefined,
                    temperature: parseFloat(temperature) || undefined,
                    supplier: supplier || undefined,
                    location: location || undefined,
                    unitPrice: parseFloat(unitPrice) || undefined,
                    origin: origin || undefined,
                });
                break;
            case 'relocation':
                 Object.assign(eventData, {
                    product, productForm,
                    quantity: parseFloat(quantity) || undefined,
                    unit: 'lbs',
                    batchNumber: batchNumber || undefined,
                    fromLocation: fromLocation || undefined,
                    location: location || undefined,
                 });
                 break;
            case 'sanitation':
                Object.assign(eventData, { areaCleaned, sanitizerUsed });
                break;
            case 'thermometer-calibration':
                Object.assign(eventData, { thermometerId, calibrationMethod, result });
                break;
             case 'employee-training':
                Object.assign(eventData, { employeeName, trainingTopic });
                break;
        }

        try {
            if (editingEventId) {
                const eventRef = doc(db, 'events', editingEventId);
                await updateDoc(eventRef, eventData);
            } else {
                await addDoc(collection(db, 'events'), eventData);
            }
            resetFormAndEditingState();
            setView('dashboard');
        } catch (error) {
            console.error("Error saving event: ", error);
            alert('Failed to save event.');
        }
    };

    const handleEditEvent = (event: HACCPEvent) => {
        setEditingEventId(event.id);
        setEventType(event.eventType);
        setDate(event.date);
        setTime(event.time);
        setBatchNumber(event.batchNumber || '');
        setProduct(event.product || '');
        setProductForm(event.productForm || '');
        setQuantity(event.quantity?.toString() || '');
        setTemperature(event.temperature?.toString() || '');
        setSupplier(event.supplier || '');
        setLocation(event.location || '');
        setFromLocation(event.fromLocation || '');
        setNotes(event.notes || '');
        setAreaCleaned(event.areaCleaned || '');
        setSanitizerUsed(event.sanitizerUsed || '');
        setThermometerId(event.thermometerId || '');
        setCalibrationMethod(event.calibrationMethod || 'Ice Point');
        setResult(event.result || 'pass');
        setCorrectiveAction(event.correctiveAction || '');
        setEmployeeName(event.employeeName || '');
        setTrainingTopic(event.trainingTopic || '');
        setImageFile(null);
        setImagePreview(event.imageUrl || null);
        setImageDescription(event.imageDescription || '');
        setView('form');
    };
    
    const handleUpdateEventField = async (eventId: string, field: keyof HACCPEvent, value: any): Promise<boolean> => {
        try {
            const eventRef = doc(db, "events", eventId);
            await updateDoc(eventRef, { [field]: value });
            return true;
        } catch (error) {
            console.error("Failed to update event field:", error);
            return false;
        }
    };
    
    const handleOpenSelectKey = async () => {
        if ((window as any).aistudio) {
            await (window as any).aistudio.openSelectKey();
            setApiKeySelected(true);
        }
    };

    // Voice Assistant Logic
    const systemInstruction = `You are a voice assistant for a seafood inventory management app. Your primary role is to help users log HACCP events, manage data, and get information through voice commands.
- You can create, update, and inquire about inventory events like receiving, sales, disposal, and more.
- You can manage vendors, species, locations, and purchase orders.
- You can answer questions about the current inventory levels, check stock in specific locations, and summarize recent activities.
- When a user asks a general question or a question that can't be answered by the available functions, provide a helpful, conversational response.
- Today's date is ${new Date().toLocaleDateString()}. Use this for any date-related queries unless the user specifies otherwise.
- Before calling a function, confirm any ambiguous details with the user. For example, if they say "log a sale of salmon", ask for the quantity.
- After a function is successfully called, provide a clear confirmation message to the user, like "Done. I've logged the sale of 15 lbs of Salmon."
- If the user's request is unclear, ask for clarification.
The following functions are available to manage the application state. Call them when the user's intent matches the function's description.
`;

    const functionDeclarations: FunctionDeclaration[] = useMemo(() => [
        {
            name: 'log_haccp_event',
            description: 'Logs a new HACCP event such as receiving, sales, disposal, sanitation, etc.',
            parameters: {
                type: Type.OBJECT,
                properties: {
                    eventType: { type: Type.STRING, enum: ['receiving', 'sales', 'disposal', 're-sealing', 'sanitation', 'thermometer-calibration', 'inventory', 'employee-training', 'relocation'] },
                    product: { type: Type.STRING, description: `The specific seafood product. Must be one of: ${species.map(s => s.name).join(', ')}` },
                    productForm: { type: Type.STRING, description: 'The form of the product, e.g., Fillet, Whole.' },
                    quantity: { type: Type.NUMBER, description: 'The weight of the product in pounds (lbs).' },
                    temperature: { type: Type.NUMBER, description: 'The temperature of the product in Fahrenheit.' },
                    supplier: { type: Type.STRING, description: `The supplier of the product. Must be one of: ${vendors.map(v => v.name).join(', ')}` },
                    location: { type: Type.STRING, description: `The storage location for the product. Must be one of: ${locations.map(l => l.name).join(', ')}` },
                    fromLocation: { type: Type.STRING, description: `For relocation events, the location the product is coming from. Must be one of: ${locations.map(l => l.name).join(', ')}` },
                    areaCleaned: { type: Type.STRING, description: 'For sanitation events, the area that was cleaned.' },
                    employeeName: { type: Type.STRING, description: 'For training events, the name of the employee.'},
                    trainingTopic: { type: Type.STRING, description: 'For training events, the topic covered.'},
                    notes: { type: Type.STRING, description: 'Any additional notes for the event.' },
                },
                required: ['eventType']
            }
        },
        {
            name: 'get_inventory_summary',
            description: 'Provides a summary of the current inventory levels for all products.',
            parameters: { type: Type.OBJECT, properties: {} }
        },
        {
            name: 'check_stock_at_location',
            description: 'Checks the inventory of all products at a specific location.',
            parameters: {
                type: Type.OBJECT,
                properties: {
                    locationName: { type: Type.STRING, description: `The location to check. Must be one of: ${locations.map(l => l.name).join(', ')}` },
                },
                required: ['locationName']
            }
        },
        {
            name: 'create_purchase_order',
            description: 'Creates a new purchase order plan.',
            parameters: {
                type: Type.OBJECT,
                properties: {
                    vendorName: { type: Type.STRING, description: `The name of the vendor. Must be one of: ${vendors.map(v => v.name).join(', ')}` },
                    speciesName: { type: Type.STRING, description: `The species to order. Must be one of: ${species.map(s => s.name).join(', ')}` },
                    quantity: { type: Type.NUMBER, description: 'The quantity to order in pounds (lbs).' },
                    expectedDeliveryDate: { type: Type.STRING, description: 'The expected delivery date in YYYY-MM-DD format.' },
                },
                required: ['vendorName', 'speciesName', 'quantity', 'expectedDeliveryDate']
            }
        },
        {
            name: 'find_events',
            description: 'Finds and retrieves past events based on criteria like product, date, or event type. Returns event IDs that can be used for editing.',
            parameters: {
                type: Type.OBJECT,
                properties: {
                    product: { type: Type.STRING, description: `Filter by product name. Must be one of: ${species.map(s => s.name).join(', ')}` },
                    eventType: { type: Type.STRING, enum: ['receiving', 'sales', 'disposal', 're-sealing', 'sanitation', 'thermometer-calibration', 'inventory', 'employee-training', 'relocation'] },
                    date: { type: Type.STRING, description: 'Filter by a specific date (YYYY-MM-DD).' },
                    supplier: { type: Type.STRING, description: `Filter by supplier name. Must be one of: ${vendors.map(v => v.name).join(', ')}` },
                    location: { type: Type.STRING, description: `Filter by location. Must be one of: ${locations.map(l => l.name).join(', ')}` },
                },
            }
        },
        {
            name: 'update_event',
            description: 'Updates an existing HACCP event. Use find_events first to get the event ID, then call this function to update specific fields.',
            parameters: {
                type: Type.OBJECT,
                properties: {
                    eventId: { type: Type.STRING, description: 'The ID of the event to update (obtained from find_events).' },
                    product: { type: Type.STRING, description: `The specific seafood product. Must be one of: ${species.map(s => s.name).join(', ')}` },
                    productForm: { type: Type.STRING, description: 'The form of the product, e.g., Fillet, Whole.' },
                    quantity: { type: Type.NUMBER, description: 'The weight of the product in pounds (lbs).' },
                    temperature: { type: Type.NUMBER, description: 'The temperature of the product in Fahrenheit.' },
                    supplier: { type: Type.STRING, description: `The supplier of the product. Must be one of: ${vendors.map(v => v.name).join(', ')}` },
                    location: { type: Type.STRING, description: `The storage location for the product. Must be one of: ${locations.map(l => l.name).join(', ')}` },
                    notes: { type: Type.STRING, description: 'Any additional notes for the event.' },
                },
                required: ['eventId']
            }
        },
    ], [species, vendors, locations, inventory, inventoryByLocation, purchaseOrders]);
    
    const logHACCPEvent = async (args: any) => {
        const newEvent: Partial<HACCPEvent> = {
            ...args,
            date: args.date || new Date().toISOString().split('T')[0],
            time: new Date().toTimeString().substring(0, 5),
            createdBy: currentUser,
            createdAt: serverTimestamp(),
        };

        if (newEvent.quantity) newEvent.unit = 'lbs';

        await addDoc(collection(db, 'events'), newEvent);
        return `Done. I've logged the ${args.eventType} event.`;
    };

    const getInventorySummary = () => {
        const summary = Object.entries(inventory)
            .map(([product, quantity]) => `${quantity.toFixed(2)} lbs of ${product}`)
            .join(', ');
        return summary ? `Current inventory is: ${summary}.` : 'The inventory is currently empty.';
    };

    const checkStockAtLocation = (args: { locationName: string }) => {
        const { locationName } = args;
        const locationInventory = inventoryByLocation[locationName];
        if (!locationInventory || Object.keys(locationInventory).length === 0) {
            return `There is no inventory currently at ${locationName}.`;
        }
        const summary = Object.entries(locationInventory)
            .map(([product, quantity]) => `${quantity.toFixed(2)} lbs of ${product}`)
            .join(', ');
        return `At ${locationName}, we have: ${summary}.`;
    };

    const createPurchaseOrder = async (args: any) => {
        const vendor = vendors.find(v => v.name.toLowerCase() === args.vendorName.toLowerCase());
        if (!vendor) return `Vendor "${args.vendorName}" not found.`;
        
        const orderData = {
            vendorId: vendor.id,
            vendorName: vendor.name,
            species: args.speciesName,
            quantity: args.quantity,
            unit: 'lbs',
            expectedDeliveryDate: args.expectedDeliveryDate,
            status: 'planning',
            createdAt: serverTimestamp(),
        };

        await addDoc(collection(db, "purchaseOrders"), orderData);
        return `Okay, I've created a planned purchase order for ${args.quantity} lbs of ${args.speciesName} from ${args.vendorName}.`;
    };

    const findEvents = (args: any) => {
        let results = [...events];
        if (args.product) results = results.filter(e => e.product?.toLowerCase() === args.product.toLowerCase());
        if (args.eventType) results = results.filter(e => e.eventType === args.eventType);
        if (args.date) results = results.filter(e => e.date === args.date);
        if (args.supplier) results = results.filter(e => e.supplier?.toLowerCase() === args.supplier.toLowerCase());
        if (args.location) results = results.filter(e => e.location?.toLowerCase() === args.location.toLowerCase());

        if (results.length === 0) return 'I could not find any events matching your criteria.';

        const summary = results.slice(0, 5).map(e => {
            let details = `Event ID ${e.id}: On ${e.date}, a ${e.eventType} event`;
            if (e.product) details += ` for ${e.quantity || ''} ${e.unit || ''} of ${e.product}`;
            if (e.supplier) details += ` from ${e.supplier}`;
            if (e.location) details += ` at ${e.location}`;
            return details;
        }).join('. ');

        return `I found ${results.length} events. Here are the first few: ${summary}. Use the Event ID to update any of these events.`;
    };

    const updateEvent = async (args: any) => {
        const { eventId, ...updates } = args;

        if (!eventId) return 'Error: Event ID is required to update an event.';

        // Find the event to make sure it exists
        const event = events.find(e => e.id === eventId);
        if (!event) return `Error: Could not find event with ID ${eventId}.`;

        // Only update fields that were provided
        const updateData: any = {};
        if (updates.product !== undefined) updateData.product = updates.product;
        if (updates.productForm !== undefined) updateData.productForm = updates.productForm;
        if (updates.quantity !== undefined) {
            updateData.quantity = updates.quantity;
            updateData.unit = 'lbs';
        }
        if (updates.temperature !== undefined) updateData.temperature = updates.temperature;
        if (updates.supplier !== undefined) updateData.supplier = updates.supplier;
        if (updates.location !== undefined) updateData.location = updates.location;
        if (updates.notes !== undefined) updateData.notes = updates.notes;

        // Add updatedAt timestamp
        updateData.updatedAt = serverTimestamp();

        try {
            await updateDoc(doc(db, 'events', eventId), updateData);
            return `Done. I've updated the ${event.eventType} event from ${event.date}.`;
        } catch (error: any) {
            return `Error updating event: ${error.message}`;
        }
    };
    
    const processFunctionCall = async (fc: any) => {
        let result: any;
        let functionResponseText = '';
        const functionName = fc.name;
        const args = fc.args;

        switch (functionName) {
            case 'log_haccp_event':
                functionResponseText = await logHACCPEvent(args);
                result = { result: "Event logged successfully." };
                break;
            case 'get_inventory_summary':
                functionResponseText = getInventorySummary();
                result = { result: "Displayed inventory summary." };
                break;
            case 'check_stock_at_location':
                functionResponseText = checkStockAtLocation(args);
                result = { result: "Displayed stock for location." };
                break;
            case 'create_purchase_order':
                functionResponseText = await createPurchaseOrder(args);
                result = { result: "Purchase order created." };
                break;
            case 'find_events':
                functionResponseText = findEvents(args);
                result = { result: "Found and summarized events." };
                break;
            case 'update_event':
                functionResponseText = await updateEvent(args);
                result = { result: "Event updated successfully." };
                break;
            default:
                console.warn(`Unknown function call: ${functionName}`);
                result = { error: "Unknown function" };
                functionResponseText = `Sorry, I can't do that.`;
        }

        return { functionResponseText, result };
    };

    const startSession = useCallback(async () => {
        setStatus('Connecting...');
        setConversation([]);
        currentInputTranscriptionRef.current = '';
        currentOutputTranscriptionRef.current = '';

        if (!apiKeySelected) {
            setStatus('Please select an API key first.');
            return;
        }

        try {
            const ai = new GoogleGenAI({ apiKey: import.meta.env.VITE_GEMINI_API_KEY });
            
            inputAudioContextRef.current = new AudioContext({ sampleRate: 16000 });
            outputAudioContextRef.current = new AudioContext({ sampleRate: 24000 });
            
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            streamRef.current = stream;

            sessionPromiseRef.current = ai.live.connect({
                model: 'gemini-2.5-flash-native-audio-preview-09-2025',
                callbacks: {
                    onopen: () => {
                        setStatus('Listening... Speak now.');
                        setIsSessionActive(true);

                        const source = inputAudioContextRef.current!.createMediaStreamSource(stream);
                        mediaStreamSourceRef.current = source;
                        
                        // Use ScriptProcessor for wider browser support
                        const scriptProcessor = inputAudioContextRef.current!.createScriptProcessor(4096, 1, 1);
                        scriptProcessor.onaudioprocess = (audioProcessingEvent) => {
                            const inputData = audioProcessingEvent.inputBuffer.getChannelData(0);
                            const pcmBlob = createBlob(inputData);
                            sessionPromiseRef.current?.then((session) => {
                                session.sendRealtimeInput({ media: pcmBlob });
                            });
                        };
                        source.connect(scriptProcessor);
                        scriptProcessor.connect(inputAudioContextRef.current!.destination); // This is necessary for onaudioprocess to fire in some browsers.
                    },
                    onmessage: async (message: LiveServerMessage) => {
                        if (message.serverContent?.outputTranscription) {
                            const text = message.serverContent.outputTranscription.text;
                            currentOutputTranscriptionRef.current += text;
                        } else if (message.serverContent?.inputTranscription) {
                            const text = message.serverContent.inputTranscription.text;
                            currentInputTranscriptionRef.current += text;
                        }

                        if (message.serverContent?.turnComplete) {
                             const fullInput = currentInputTranscriptionRef.current;
                             const fullOutput = currentOutputTranscriptionRef.current;
                             
                             if (fullInput) {
                                setConversation(prev => [...prev, { speaker: 'user', text: fullInput }]);
                             }
                             if (fullOutput) {
                                setConversation(prev => [...prev, { speaker: 'model', text: fullOutput }]);
                             }
                             
                             currentInputTranscriptionRef.current = '';
                             currentOutputTranscriptionRef.current = '';
                        }
                        
                        if(message.toolCall) {
                            for (const fc of message.toolCall.functionCalls) {
                                const { functionResponseText, result } = await processFunctionCall(fc);
                                setConversation(prev => [...prev, { speaker: 'model', text: functionResponseText }]);
                                
                                sessionPromiseRef.current?.then((session) => {
                                    session.sendToolResponse({
                                        functionResponses: {
                                            id: fc.id,
                                            name: fc.name,
                                            response: result,
                                        }
                                    });
                                });
                            }
                        }

                        const base64Audio = message.serverContent?.modelTurn?.parts[0]?.inlineData?.data;
                        if (base64Audio) {
                            const outputContext = outputAudioContextRef.current!;
                            nextStartTimeRef.current = Math.max(nextStartTimeRef.current, outputContext.currentTime);
                            
                            const audioBuffer = await decodeAudioData(
                                decode(base64Audio),
                                outputContext,
                                24000,
                                1,
                            );

                            const source = outputContext.createBufferSource();
                            source.buffer = audioBuffer;
                            source.connect(outputContext.destination);
                            
                            source.addEventListener('ended', () => {
                                sourcesRef.current.delete(source);
                            });
                            
                            source.start(nextStartTimeRef.current);
                            nextStartTimeRef.current += audioBuffer.duration;
                            sourcesRef.current.add(source);
                        }
                        
                        const interrupted = message.serverContent?.interrupted;
                        if (interrupted) {
                            for (const source of sourcesRef.current.values()) {
                                source.stop();
                                sourcesRef.current.delete(source);
                            }
                            nextStartTimeRef.current = 0;
                        }

                    },
                    onerror: (e: ErrorEvent) => {
                        console.error('Session error:', e);
                        setStatus('Connection error. Please try again.');
                        if (e.message?.includes('Requested entity was not found')) {
                            setApiKeySelected(false);
                            setStatus('API Key Error. Please re-select your key.');
                        }
                        stopSession();
                    },
                    onclose: (e: CloseEvent) => {
                       // This is an expected event when stopSession() is called.
                       // We can reset the state here.
                       setIsSessionActive(false);
                       setStatus('Idle. Press Start to begin.');
                    },
                },
                config: {
                    responseModalities: [Modality.AUDIO],
                    outputAudioTranscription: {},
                    inputAudioTranscription: {},
                    tools: [{ functionDeclarations }],
                    systemInstruction: systemInstruction,
                },
            });

        } catch (error) {
            console.error('Failed to start session:', error);
            setStatus('Failed to start. Check mic permissions.');
            stopSession();
        }
    }, [apiKeySelected, functionDeclarations, systemInstruction]);

    const stopSession = useCallback(() => {
        sessionPromiseRef.current?.then((session) => {
            session.close();
        });
        sessionPromiseRef.current = null;
        
        streamRef.current?.getTracks().forEach(track => track.stop());
        streamRef.current = null;
        
        mediaStreamSourceRef.current?.disconnect();
        mediaStreamSourceRef.current = null;
        
        workletNodeRef.current?.disconnect();
        workletNodeRef.current = null;

        inputAudioContextRef.current?.close().catch(console.error);
        inputAudioContextRef.current = null;

        setIsSessionActive(false);
        setStatus('Idle. Press Start to begin.');
    }, []);
    
    const handleCaptureAndSave = (events: Partial<HACCPEvent>[]) => {
        const batch = writeBatch(db);
        const newSpecies = new Set<string>();
        const newVendors = new Set<string>();
        const newLocations = new Set<string>();

        const existingSpecies = new Set(species.map(s => s.name.toLowerCase()));
        const existingVendors = new Set(vendors.map(v => v.name.toLowerCase()));
        const existingLocations = new Set(locations.map(l => l.name.toLowerCase()));

        events.forEach(event => {
            const eventRef = doc(collection(db, "events"));
            const completeEvent = {
                ...event,
                time: new Date().toTimeString().substring(0, 5),
                createdBy: currentUser,
                createdAt: serverTimestamp(),
            };
            if(!completeEvent.date) completeEvent.date = new Date().toISOString().split('T')[0];
            batch.set(eventRef, completeEvent);

            if (event.product && !existingSpecies.has(event.product.toLowerCase())) newSpecies.add(event.product);
            if (event.supplier && !existingVendors.has(event.supplier.toLowerCase())) newVendors.add(event.supplier);
            if (event.location && !existingLocations.has(event.location.toLowerCase())) newLocations.add(event.location);
        });
        
        newSpecies.forEach(name => batch.set(doc(collection(db, "species")), { name, productForms: [], qualityControlNotes: 'N/A' }));
        newVendors.forEach(name => batch.set(doc(collection(db, "vendors")), { name }));
        newLocations.forEach(name => batch.set(doc(collection(db, "locations")), { name }));

        batch.commit().then(() => {
            setIsCameraModalOpen(false);
            setView('dashboard');
        }).catch(error => {
            console.error("Failed to save batch data:", error);
            alert("Failed to save data. Check console for details.");
        });
    };
    
    const handleImportAndSave = (events: Partial<HACCPEvent>[]) => {
        handleCaptureAndSave(events); // Re-use the same batch save logic
        setIsImportModalOpen(false);
    };

    const renderView = () => {
        switch (view) {
            case 'form':
                const formTitle = editingEventId ? 'Edit HACCP Event' : 'New HACCP Event';
                return (
                    <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 max-w-2xl w-full mx-auto">
                        <div className="flex justify-between items-center mb-6 pb-4 border-b">
                            <h1 className="text-2xl font-bold text-gray-800">{formTitle}</h1>
                            {editingEventId && <button onClick={resetFormAndEditingState} className="text-sm text-blue-600 hover:underline">Cancel Edit</button>}
                        </div>
                        {/* Event Type Selector */}
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1">Event Type</label>
                            <select value={eventType} onChange={(e) => setEventType(e.target.value as EventType)} className="w-full p-2 border border-gray-300 rounded-md shadow-sm">
                                <option value="receiving">Receiving</option>
                                <option value="sales">Sales</option>
                                <option value="disposal">Disposal</option>
                                <option value="re-sealing">Re-sealing</option>
                                <option value="relocation">Relocation</option>
                                <option value="inventory">Inventory Count</option>
                                <option value="sanitation">Sanitation</option>
                                <option value="thermometer-calibration">Thermometer Calibration</option>
                                <option value="employee-training">Employee Training</option>
                            </select>
                        </div>

                        {/* Common Fields */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Date</label>
                                <input type="date" value={date} onChange={(e) => setDate(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Time</label>
                                <input type="time" value={time} onChange={(e) => setTime(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                            </div>
                        </div>

                        {/* Conditional Fields */}
                        {(eventType === 'receiving' || eventType === 'sales' || eventType === 'disposal' || eventType === 're-sealing' || eventType === 'inventory' || eventType === 'relocation') && (
                             <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Product/Species</label>
                                    <select value={product} onChange={e => setProduct(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm">
                                        <option value="">-- Select Species --</option>
                                        {species.map(s => <option key={s.id} value={s.name}>{s.name}</option>)}
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Product Form</label>
                                    <select value={productForm} onChange={e => setProductForm(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm" disabled={!product}>
                                        <option value="">-- Select Form --</option>
                                        {(species.find(s => s.name === product)?.productForms || []).map(form => <option key={form} value={form}>{form}</option>)}
                                    </select>
                                </div>
                                 <div>
                                    <label className="block text-sm font-medium text-gray-700">Quantity (lbs)</label>
                                    <input type="number" value={quantity} onChange={(e) => setQuantity(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm" placeholder="e.g., 50.5"/>
                                </div>
                                {eventType === 'receiving' && (
                                     <div>
                                        <label className="block text-sm font-medium text-gray-700">Temperature (°F)</label>
                                        <input type="number" value={temperature} onChange={(e) => setTemperature(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm" />
                                    </div>
                                )}
                                {eventType === 'receiving' && (
                                    <>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">Unit Price ($)</label>
                                            <input type="number" value={unitPrice} onChange={(e) => setUnitPrice(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm" placeholder="e.g., 22.50"/>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">Origin</label>
                                            <input type="text" value={origin} onChange={(e) => setOrigin(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm" placeholder="e.g., Alaska"/>
                                        </div>
                                    </>
                                )}
                                {(eventType === 'receiving' || eventType === 'inventory') && (
                                    <div className="col-span-1 sm:col-span-2">
                                        <label className="block text-sm font-medium text-gray-700">Batch Number</label>
                                        <input type="text" value={batchNumber} onChange={(e) => setBatchNumber(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm font-mono text-sm" placeholder="Auto-generated or manual"/>
                                    </div>
                                )}
                                {eventType === 'receiving' && (
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Supplier</label>
                                        <select value={supplier} onChange={(e) => setSupplier(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm">
                                            <option value="">-- Select Vendor --</option>
                                            {vendors.map(v => <option key={v.id} value={v.name}>{v.name}</option>)}
                                        </select>
                                    </div>
                                )}
                                {eventType === 'relocation' && (
                                     <div>
                                        <label className="block text-sm font-medium text-gray-700">From Location</label>
                                        <select value={fromLocation} onChange={e => setFromLocation(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm">
                                             {locations.map(l => <option key={l.id} value={l.name}>{l.name}</option>)}
                                        </select>
                                    </div>
                                )}
                                {(eventType === 'receiving' || eventType === 'sales' || eventType === 'disposal' || eventType === 'relocation' || eventType === 'inventory') && (
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">{eventType === 'relocation' ? 'To Location' : 'Location'}</label>
                                        <select value={location} onChange={e => setLocation(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm">
                                             {locations.map(l => <option key={l.id} value={l.name}>{l.name}</option>)}
                                        </select>
                                    </div>
                                )}
                            </div>
                        )}
                        {eventType === 'sanitation' && (
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Area/Equipment Cleaned</label>
                                    <input type="text" value={areaCleaned} onChange={e => setAreaCleaned(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Sanitizer Used</label>
                                    <input type="text" value={sanitizerUsed} onChange={e => setSanitizerUsed(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                                </div>
                            </div>
                        )}
                        {eventType === 'thermometer-calibration' && (
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Thermometer ID</label>
                                    <input type="text" value={thermometerId} onChange={e => setThermometerId(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                                </div>
                                 <div>
                                    <label className="block text-sm font-medium text-gray-700">Calibration Method</label>
                                    <select value={calibrationMethod} onChange={e => setCalibrationMethod(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm">
                                        <option>Ice Point</option>
                                        <option>Boiling Point</option>
                                    </select>
                                </div>
                                <div className="col-span-1 sm:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700">Result</label>
                                    <div className="flex items-center space-x-4 mt-1">
                                        <label><input type="radio" value="pass" checked={result === 'pass'} onChange={() => setResult('pass')} className="mr-1"/> Pass</label>
                                        <label><input type="radio" value="fail" checked={result === 'fail'} onChange={() => setResult('fail')} className="mr-1"/> Fail</label>
                                    </div>
                                </div>
                            </div>
                        )}
                        {eventType === 'employee-training' && (
                             <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Employee Name</label>
                                    <input type="text" value={employeeName} onChange={e => setEmployeeName(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Training Topic</label>
                                    <input type="text" value={trainingTopic} onChange={e => setTrainingTopic(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                                </div>
                            </div>
                        )}
                        
                        {/* Image Upload */}
                         <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700">Attach Image</label>
                            <div className="mt-2 flex items-center space-x-4">
                                <div className="shrink-0">
                                    {imagePreview ? (
                                        <img className="h-20 w-20 object-cover rounded-md" src={imagePreview} alt="Preview" />
                                    ) : (
                                        <div className="h-20 w-20 bg-gray-100 rounded-md flex items-center justify-center">
                                            <PhotoIcon className="h-10 w-10 text-gray-400" />
                                        </div>
                                    )}
                                </div>
                                <div className="flex-grow">
                                    <input type="file" onChange={handleImageSelect} accept="image/*" className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"/>
                                     {imageFile && (
                                         <button onClick={handleImageAnalysis} disabled={isAnalyzing} className="mt-2 flex items-center text-sm text-indigo-600 hover:text-indigo-800 disabled:opacity-50">
                                             <SparklesIcon className="h-4 w-4 mr-1"/>
                                             {isAnalyzing ? 'Analyzing...' : 'Analyze with AI'}
                                         </button>
                                     )}
                                </div>
                            </div>
                            {imageDescription && (
                                <div className="mt-2">
                                    <label className="block text-sm font-medium text-gray-700">AI Analysis / Image Description</label>
                                    <textarea value={imageDescription} onChange={e => setImageDescription(e.target.value)} rows={3} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm bg-gray-50"></textarea>
                                </div>
                            )}
                        </div>

                        {/* Notes & Corrective Action */}
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700">Notes</label>
                            <textarea value={notes} onChange={(e) => setNotes(e.target.value)} rows={3} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"></textarea>
                        </div>
                        <div className="mb-6">
                            <label className="block text-sm font-medium text-gray-700">Corrective Action</label>
                            <textarea value={correctiveAction} onChange={(e) => setCorrectiveAction(e.target.value)} rows={3} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"></textarea>
                        </div>

                        <div className="flex justify-end">
                            <button onClick={handleSubmit} className="bg-blue-600 text-white px-6 py-2 rounded-md shadow-sm hover:bg-blue-700">
                                {editingEventId ? 'Update Event' : 'Save Event'}
                            </button>
                        </div>
                    </div>
                );
            case 'dashboard':
                return <Dashboard events={events} species={species} vendors={vendors} locations={locations} onEditEvent={handleEditEvent} onDraftEmail={setEmailContext} onUpdateEventField={handleUpdateEventField}/>;
            case 'haccp':
                return <HaccpLogsView />;
            case 'inventory':
                return <InventoryManagement events={events} species={species} vendors={vendors} />;
            case 'reports':
                return <ReportsView />;
            case 'settings':
                return <SettingsView onNavigate={setView} />;
            case 'calendar':
                return <CalendarView events={events} onAddNewEvent={(date) => { resetFormAndEditingState(); setDate(date); setView('form');}} />;
            case 'vendors':
                return <VendorsView />;
            case 'species':
                return <SpeciesView />;
            case 'locations':
                return <LocationsView events={events} />;
            case 'orders':
                return <OrdersView purchaseOrders={purchaseOrders} vendors={vendors} species={species} onDraftEmail={setEmailContext} />;
            case 'temperature':
                return <TemperatureView />;
            case 'lot-tracking':
                return <LotTrackingView />;
            default:
                return <div>Select a view</div>;
        }
    };

    // Show loading spinner while checking auth
    if (authLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600 dark:text-gray-400">Loading...</p>
                </div>
            </div>
        );
    }

    // Show login screen if not authenticated
    if (!user) {
        return <Login onLoginSuccess={() => {}} />;
    }

    return (
        <div className="relative min-h-screen w-full font-display bg-background-light dark:bg-background-dark text-text-light dark:text-text-dark">
            <Sidebar currentView={view} onNavigate={setView} onCollapseChange={setIsSidebarCollapsed} />
            <main className={`flex-1 p-8 transition-all duration-300 ease-in-out ${isSidebarCollapsed ? 'md:ml-16' : 'md:ml-64'}`}>
                <div className="flex flex-col gap-8 max-w-7xl mx-auto">
                    <header className="flex flex-wrap justify-between items-center gap-4">
                        <div className="flex items-center gap-4">
                            {/* Mobile hamburger menu button - only show when sidebar is collapsed */}
                            {isSidebarCollapsed && (
                                <button
                                    onClick={() => setIsSidebarCollapsed(false)}
                                    className="md:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                                    aria-label="Open sidebar"
                                >
                                    <HamburgerIcon className="h-6 w-6 text-gray-600 dark:text-gray-400" />
                                </button>
                            )}
                            <div className="flex flex-col gap-2">
                                <h1 className="text-text-light dark:text-text-dark text-4xl font-black tracking-tighter capitalize">{view}</h1>
                                <p className="text-gray-500 dark:text-gray-400 text-base font-normal leading-normal">Manage your operations from here.</p>
                            </div>
                        </div>
                        <div className="flex items-center gap-3">
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                                {user?.email}
                            </span>
                            <button
                                onClick={() => { resetFormAndEditingState(); setView('form'); }}
                                className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow hover:bg-blue-700"
                            >
                                New Event
                            </button>
                            <button
                                onClick={() => signOut(auth)}
                                className="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-lg shadow hover:bg-gray-300 dark:hover:bg-gray-600"
                            >
                                Sign Out
                            </button>
                        </div>
                    </header>
                    {renderView()}
                </div>
            </main>
            {/* Floating Action Buttons */}
            <div className="fixed bottom-6 right-6 flex flex-col items-center space-y-3 z-20">
                 <button onClick={() => setIsCameraModalOpen(true)} className="p-4 bg-gray-700 text-white rounded-full shadow-lg hover:bg-gray-800" title="Scan Document">
                    <CameraIcon className="h-7 w-7"/>
                </button>
                <button onClick={() => isSessionActive ? stopSession() : startSession()} className={`p-5 rounded-full shadow-xl transition-colors duration-200 ${isSessionActive ? 'bg-red-600 hover:bg-red-700' : 'bg-blue-600 hover:bg-blue-700'}`}>
                    {isSessionActive ? <StopIcon className="h-8 w-8 text-white" /> : <MicrophoneIcon className="h-8 w-8 text-white" />}
                </button>
            </div>

            {/* Voice Panel */}
             {isSessionActive && (
                 <div className="fixed bottom-24 sm:bottom-28 right-4 left-4 sm:left-auto sm:right-6 w-auto sm:w-full sm:max-w-md text-white p-4 space-y-4 z-10">
                     <StatusIndicator status={status} isRecording={isSessionActive} />
                     <ConversationLog turns={conversation} />
                 </div>
            )}
            
            {!apiKeySelected && (
                 <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
                    <div className="bg-white p-8 rounded-lg shadow-xl text-center">
                        <h2 className="text-2xl font-bold mb-4">API Key Required</h2>
                        <p className="mb-6 text-gray-600">This application requires a Google AI API key to function. <br/> Please select a key to continue.</p>
                        <p className="text-xs text-gray-500 mb-6">For information on billing, please visit <a href="https://ai.google.dev/gemini-api/docs/billing" target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">ai.google.dev/gemini-api/docs/billing</a>.</p>
                        <button onClick={handleOpenSelectKey} className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                            Select API Key
                        </button>
                    </div>
                </div>
            )}
            
            {isEmailModalOpen && emailContext && (
                <EmailDraftModal
                    isOpen={isEmailModalOpen}
                    onClose={() => setIsEmailModalOpen(false)}
                    context={emailContext}
                />
            )}
            
            {isCameraModalOpen && (
                 <CameraModal
                    species={species}
                    vendors={vendors}
                    locations={locations}
                    onClose={() => setIsCameraModalOpen(false)}
                    onSave={handleCaptureAndSave}
                />
            )}

            {isImportModalOpen && (
                 <ImportModal
                    species={species}
                    vendors={vendors}
                    locations={locations}
                    onClose={() => setIsImportModalOpen(false)}
                    onSave={handleImportAndSave}
                 />
            )}
        </div>
    );
};

export default App;
import React, { useState, useEffect } from 'react';
import { db } from '../firebase/config';
import { collection, addDoc, updateDoc, doc, serverTimestamp, deleteDoc } from 'firebase/firestore';
import { PurchaseOrder, Vendor, Species, EmailContext } from '../types';
import { ClipboardDocumentListIcon, PlusIcon, TrashIcon } from './IconComponents';

interface OrdersViewProps {
    purchaseOrders: PurchaseOrder[];
    vendors: Vendor[];
    species: Species[];
    onDraftEmail: (context: EmailContext) => void;
}

export const OrdersView: React.FC<OrdersViewProps> = ({ purchaseOrders, vendors, species, onDraftEmail }) => {
    const [isFormOpen, setIsFormOpen] = useState(false);
    
    // Form state
    const [vendorId, setVendorId] = useState('');
    const [speciesName, setSpeciesName] = useState('');
    const [quantity, setQuantity] = useState('');
    const [expectedDeliveryDate, setExpectedDeliveryDate] = useState(new Date().toISOString().split('T')[0]);

    useEffect(() => {
        if (vendors.length > 0 && !vendorId) {
            setVendorId(vendors[0].id);
        }
        if (species.length > 0 && !speciesName) {
            setSpeciesName(species[0].name);
        }
    }, [vendors, species]);

    const resetForm = () => {
        setVendorId(vendors.length > 0 ? vendors[0].id : '');
        setSpeciesName(species.length > 0 ? species[0].name : '');
        setQuantity('');
        setExpectedDeliveryDate(new Date().toISOString().split('T')[0]);
        setIsFormOpen(false);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        const selectedVendor = vendors.find(v => v.id === vendorId);
        if (!vendorId || !speciesName || !quantity || !expectedDeliveryDate || !selectedVendor) {
            alert("All fields are required.");
            return;
        }

        const orderData = { 
            vendorId,
            vendorName: selectedVendor.name,
            species: speciesName,
            quantity: parseFloat(quantity),
            unit: 'lbs',
            expectedDeliveryDate,
            status: 'planning',
            createdAt: serverTimestamp(),
        };

        try {
            await addDoc(collection(db, "purchaseOrders"), orderData);
            resetForm();
        } catch (error) {
            console.error("Error creating purchase order: ", error);
            alert("Failed to create purchase order.");
        }
    };
    
    const updateOrderStatus = async (orderId: string, status: 'ordered' | 'received') => {
        const orderRef = doc(db, 'purchaseOrders', orderId);
        await updateDoc(orderRef, { status });
    };

    const deleteOrder = async (orderId: string) => {
        if (window.confirm("Are you sure you want to delete this planned order?")) {
            await deleteDoc(doc(db, 'purchaseOrders', orderId));
        }
    };

    const today = new Date();
    const ordersToPlace = purchaseOrders.filter(o => {
        const vendor = vendors.find(v => v.id === o.vendorId);
        if (o.status !== 'planning' || !vendor?.sourcingLeadTimeDays) return false;
        const deliveryDate = new Date(o.expectedDeliveryDate + 'T00:00:00');
        const orderDate = new Date(deliveryDate);
        orderDate.setDate(deliveryDate.getDate() - vendor.sourcingLeadTimeDays);
        return today >= orderDate;
    });

    const plannedOrders = purchaseOrders.filter(o => o.status === 'planning' && !ordersToPlace.includes(o));
    const pendingOrders = purchaseOrders.filter(o => o.status === 'ordered');
    const receivedOrders = purchaseOrders.filter(o => o.status === 'received');

    const OrderCard: React.FC<{ order: PurchaseOrder, showActions?: boolean }> = ({ order, showActions = false }) => {
        const vendor = vendors.find(v => v.id === order.vendorId);
        return (
             <div className="p-4 border rounded-lg bg-white shadow-sm">
                <div className="flex justify-between items-start">
                    <div>
                        <p className="font-bold text-gray-800">{order.species}</p>
                        <p className="text-sm text-gray-600">{order.quantity} {order.unit}</p>
                        <p className="text-xs text-gray-500">from {order.vendorName}</p>
                    </div>
                     <p className="text-sm font-semibold text-gray-700">Due: {order.expectedDeliveryDate}</p>
                </div>
                {showActions && (
                    <div className="flex justify-end items-center space-x-2 mt-3 pt-3 border-t">
                        <button onClick={() => deleteOrder(order.id)} className="text-red-600 hover:text-red-800"><TrashIcon className="h-5 w-5"/></button>
                        {vendor && <button onClick={() => onDraftEmail({ type: 'order', order, vendor })} className="text-sm font-medium text-indigo-600 hover:text-indigo-800">Draft Email</button>}
                        <button onClick={() => updateOrderStatus(order.id, 'ordered')} className="text-sm bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700">Mark as Ordered</button>
                    </div>
                )}
            </div>
        )
    };

    return (
        <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 w-full space-y-6">
            <header className="flex items-center justify-between pb-4 border-b border-gray-200">
                <div className="flex items-center">
                    <div className="bg-orange-100 text-orange-600 p-2 rounded-lg mr-4">
                        <ClipboardDocumentListIcon className="h-6 w-6" />
                    </div>
                    <h1 className="text-2xl font-bold text-gray-800">Order Management</h1>
                </div>
                <button onClick={() => setIsFormOpen(true)} className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-blue-700">
                    <PlusIcon className="h-5 w-5 mr-2" />
                    Plan New Order
                </button>
            </header>

            {isFormOpen && (
                 <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                    <h2 className="text-xl font-semibold mb-4 text-gray-700">Plan a New Purchase Order</h2>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label htmlFor="vendorId" className="block text-sm font-medium text-gray-700">Vendor</label>
                                <select id="vendorId" value={vendorId} onChange={e => setVendorId(e.target.value)} required className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm">
                                    {vendors.map(v => <option key={v.id} value={v.id}>{v.name}</option>)}
                                </select>
                            </div>
                             <div>
                                <label htmlFor="speciesName" className="block text-sm font-medium text-gray-700">Species</label>
                                <select id="speciesName" value={speciesName} onChange={e => setSpeciesName(e.target.value)} required className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm">
                                    {species.map(s => <option key={s.id} value={s.name}>{s.name}</option>)}
                                </select>
                            </div>
                            <div>
                                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">Quantity (lbs)</label>
                                <input type="number" id="quantity" value={quantity} onChange={e => setQuantity(e.target.value)} required className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                            </div>
                            <div>
                                <label htmlFor="expectedDeliveryDate" className="block text-sm font-medium text-gray-700">Expected Delivery Date</label>
                                <input type="date" id="expectedDeliveryDate" value={expectedDeliveryDate} onChange={e => setExpectedDeliveryDate(e.target.value)} required className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                            </div>
                        </div>
                        <div className="flex justify-end space-x-3">
                            <button type="button" onClick={resetForm} className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">Cancel</button>
                            <button type="submit" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Save Plan</button>
                        </div>
                    </form>
                </div>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Action Required */}
                <section>
                    <h2 className="text-lg font-semibold mb-3 text-red-600">Action Required: Place Orders</h2>
                    <div className="space-y-3">
                        {ordersToPlace.length > 0 ? ordersToPlace.map(o => <OrderCard key={o.id} order={o} showActions />) : <p className="text-sm text-gray-500">No orders require immediate action.</p>}
                    </div>
                </section>
                {/* Planned */}
                <section>
                    <h2 className="text-lg font-semibold mb-3 text-gray-700">Planned Orders</h2>
                    <div className="space-y-3">
                         {plannedOrders.length > 0 ? plannedOrders.map(o => <OrderCard key={o.id} order={o} showActions />) : <p className="text-sm text-gray-500">No orders currently planned.</p>}
                    </div>
                </section>
                 {/* Pending */}
                <section>
                    <h2 className="text-lg font-semibold mb-3 text-gray-700">Pending Delivery</h2>
                    <div className="space-y-3">
                         {pendingOrders.length > 0 ? pendingOrders.map(o => <OrderCard key={o.id} order={o} />) : <p className="text-sm text-gray-500">No orders pending delivery.</p>}
                    </div>
                </section>
            </div>

        </div>
    );
};
import React, { useState, useEffect, useCallback } from 'react';
import { TempStickSensor } from '../types';
import { ThermometerIcon } from './IconComponents';
import { TemperatureChart } from './TemperatureChart';
import { fetchSensors } from '../services/tempstickService'; // Import the new service
import { MockDataWarning } from './MockDataWarning';

// Utility to format the time since the last update
const timeSince = (date: Date): string => {
    const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
    let interval = seconds / 31536000;
    if (interval > 1) return Math.floor(interval) + " years ago";
    interval = seconds / 2592000;
    if (interval > 1) return Math.floor(interval) + " months ago";
    interval = seconds / 86400;
    if (interval > 1) return Math.floor(interval) + " days ago";
    interval = seconds / 3600;
    if (interval > 1) return Math.floor(interval) + " hours ago";
    interval = seconds / 60;
    if (interval > 1) return Math.floor(interval) + " minutes ago";
    return Math.floor(seconds) + " seconds ago";
};

// Determines the color based on temperature
const getTempColor = (temp: number): string => {
    if (temp > 140) return 'blue'; // Safe for hot holding
    if (temp > 40) return 'red'; // Danger zone
    if (temp > 32) return 'yellow'; // Warning
    return 'blue'; // Safe for freezer/cooler
};

export const TemperatureView: React.FC = () => {
    const [sensors, setSensors] = useState<TempStickSensor[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
    const [viewingSensor, setViewingSensor] = useState<TempStickSensor | null>(null);

    const loadSensors = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            // Use the new service to fetch data
            const data = await fetchSensors();
            setSensors(data);
            setLastUpdated(new Date());
        } catch (err: any) {
            setError("Failed to load sensor data. This is a mock service; if you see this, there's an issue in the mock implementation.");
            setSensors([]);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        loadSensors(); // Fetch data immediately on component mount

        const intervalId = setInterval(() => {
            loadSensors();
        }, 300000); // Auto-refresh every 5 minutes

        return () => clearInterval(intervalId); // Cleanup on unmount
    }, [loadSensors]);


    return (
        <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 w-full max-w-6xl mx-auto space-y-6">
            <header className="flex items-center justify-between pb-4 border-b border-gray-200">
                <div className="flex items-center">
                    <div className="bg-red-100 text-red-600 p-2 rounded-lg mr-4">
                        <ThermometerIcon className="h-6 w-6" />
                    </div>
                    <h1 className="text-2xl font-bold text-gray-800">Temperature Monitoring</h1>
                </div>
                <button
                    onClick={loadSensors}
                    disabled={loading}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-blue-700 disabled:bg-blue-400"
                >
                    {loading ? 'Refreshing...' : 'Refresh'}
                </button>
            </header>
            
            <MockDataWarning />

            <div>
                 {error && <div className="text-red-700 text-center mb-4 p-3 bg-red-100 border border-red-300 rounded-md"><p className="font-bold">Error:</p><p>{error}</p></div>}
                 {loading && !error ? (
                    <p className="text-center text-gray-500 py-8">Loading sensor data...</p>
                 ) : (
                    <>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {sensors.map(sensor => {
                                const tempColor = getTempColor(sensor.last_temp);
                                return (
                                    <div 
                                        key={sensor.sensor_id} 
                                        className={`p-4 border rounded-lg bg-white shadow-md border-l-4 border-${tempColor}-500 transition-transform transform hover:scale-105 cursor-pointer`}
                                        onClick={() => setViewingSensor(sensor)}
                                        role="button"
                                        tabIndex={0}
                                        aria-label={`View history for ${sensor.sensor_name}`}
                                    >
                                        <div className="flex justify-between items-start">
                                            <h3 className="font-bold text-lg text-gray-800">{sensor.sensor_name}</h3>
                                            <div className="text-right">
                                                <p className={`text-4xl font-bold text-${tempColor}-600`}>{sensor.last_temp.toFixed(1)}°{sensor.temp_f_c?.toUpperCase() || 'F'}</p>
                                                <p className="text-sm text-gray-500">{sensor.last_humidity.toFixed(1)}% RH</p>
                                            </div>
                                        </div>
                                        <div className="mt-4 pt-4 border-t border-gray-200 text-xs text-gray-500 space-y-1">
                                            <p><strong>Battery:</strong> {sensor.battery_pct}%</p>
                                            <p><strong>Signal:</strong> {sensor.rssi < 0 ? `${sensor.rssi} dBm` : `${sensor.rssi}%`}</p>
                                            <p><strong>Last Update:</strong> {timeSince(new Date(sensor.last_checkin))}</p>
                                        </div>
                                    </div>
                                )
                            })}
                        </div>
                        {sensors.length === 0 && !error && !loading && (
                            <p className="text-center text-gray-500 py-8">No sample sensors available.</p>
                        )}
                    </>
                 )}
                 <div className="text-center mt-6">
                    <p className="text-xs text-gray-400">
                        Last refreshed: {lastUpdated ? lastUpdated.toLocaleTimeString() : 'N/A'}.
                    </p>
                </div>
            </div>
            {viewingSensor && (
                <TemperatureChart 
                    sensor={viewingSensor}
                    onClose={() => setViewingSensor(null)}
                />
            )}
        </div>
    );
};

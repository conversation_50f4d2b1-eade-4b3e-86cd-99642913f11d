import React, { useState } from 'react';
import { HamburgerIcon } from './IconComponents';

type ViewType = 'dashboard' | 'lot-tracking' | 'inventory' | 'haccp' | 'calendar' | 'reports' | 'settings';

interface SidebarProps {
  currentView: ViewType;
  onNavigate: (view: ViewType) => void;
  onCollapseChange?: (collapsed: boolean) => void;
}

const NavLink: React.FC<{
  isActive: boolean;
  onClick: () => void;
  icon: string;
  label: string;
  isCollapsed: boolean;
}> = ({ isActive, onClick, icon, label, isCollapsed }) => (
  <a
    href="#"
    onClick={(e) => {
      e.preventDefault();
      onClick();
    }}
    title={isCollapsed ? label : undefined}
    className={`flex items-center ${isCollapsed ? 'justify-center px-2 py-2' : 'gap-3 px-3 py-2'} rounded-lg font-medium text-sm transition-colors ${
      isActive
        ? 'bg-primary/10 text-primary dark:text-secondary dark:bg-secondary/20 font-bold'
        : 'hover:bg-gray-100 dark:hover:bg-white/10 text-text-light dark:text-text-dark'
    }`}
  >
    <span className="material-symbols-outlined">{icon}</span>
    <p className={`leading-normal ${isCollapsed ? 'hidden' : 'block'}`}>{label}</p>
  </a>
);

export const Sidebar: React.FC<SidebarProps> = ({ currentView, onNavigate, onCollapseChange }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const handleToggleCollapse = () => {
    const newCollapsed = !isCollapsed;
    setIsCollapsed(newCollapsed);
    onCollapseChange?.(newCollapsed);
  };

  return (
    <>
      {/* Mobile backdrop overlay */}
      {!isCollapsed && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
          onClick={() => {
            setIsCollapsed(true);
            onCollapseChange?.(true);
          }}
        />
      )}

      <aside className={`flex h-screen flex-col bg-card-light dark:bg-card-dark p-4 border-r border-border-light dark:border-border-dark transition-all duration-300 ease-in-out ${isCollapsed ? 'w-16' : 'w-64'} fixed inset-y-0 left-0 z-30 transform ${isCollapsed ? '-translate-x-full' : 'translate-x-0'} md:fixed md:top-0 md:translate-x-0`}>
      {/* Hamburger menu button */}
      <div className="flex justify-between items-center mb-4">
        <button
          type="button"
          onClick={handleToggleCollapse}
          className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-white/10 transition-colors"
          title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          <HamburgerIcon className="w-5 h-5" />
        </button>
      </div>

      {/* Company logo section */}
      <div className="flex flex-col gap-4 mb-8 overflow-hidden">
        {isCollapsed ? (
          <div className="flex justify-center">
            <div
              className="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
              style={{
                backgroundImage:
                  'url("https://lh3.googleusercontent.com/aida-public/AB6AXuD62ZxrENcl7YNKEE-vAvUj-yiHmqQnR7RnjPLN9dlQftGZlrzUiSjqj3a3eoWOnLNGJoPZbxGz5ES_LAi5qsNwTPfQKTUXwuQUwvmR1JluCvopQlCZo-t0EAkDtnxDNMfWbqPZbni5-WQk5xsZHwbk86ldgad0_ToTjBnaNAHO3mj8gqENo-CCfdmoWe9Cd7ntzrc4Ej0a3lbduE1rOGC3xt-R4bfOo200ocVxd3pa6fDzGVWb82vg3d-aXIRJACwkYW_Ao14zu9k")',
              }}
              data-alt="Company logo for Atlantic Seafoods"
            ></div>
          </div>
        ) : (
          <div className="flex items-center gap-3 whitespace-nowrap">
            <div
              className="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
              style={{
                backgroundImage:
                  'url("https://lh3.googleusercontent.com/aida-public/AB6AXuD62ZxrENcl7YNKEE-vAvUj-yiHmqQnR7RnjPLN9dlQftGZlrzUiSjqj3a3eoWOnLNGJoPZbxGz5ES_LAi5qsNwTPfQKTUXwuQUwvmR1JluCvopQlCZo-t0EAkDtnxDNMfWbqPZbni5-WQk5xsZHwbk86ldgad0_ToTjBnaNAHO3mj8gqENo-CCfdmoWe9Cd7ntzrc4Ej0a3lbduE1rOGC3xt-R4bfOo200ocVxd3pa6fDzGVWb82vg3d-aXIRJACwkYW_Ao14zu9k")',
              }}
              data-alt="Company logo for Atlantic Seafoods"
            ></div>
            <div className="flex flex-col">
              <h1 className="text-text-light dark:text-text-dark text-base font-bold leading-normal">
                Atlantic Seafoods
              </h1>
              <p className="text-gray-500 dark:text-gray-400 text-sm font-normal leading-normal">
                Admin
              </p>
            </div>
          </div>
        )}
      </div>
      <nav className="flex flex-col gap-2">
        <NavLink
          isActive={currentView === 'dashboard'}
          onClick={() => onNavigate('dashboard')}
          icon="dashboard"
          label="Dashboard"
          isCollapsed={isCollapsed}
        />
        <NavLink
          isActive={currentView === 'lot-tracking'}
          onClick={() => onNavigate('lot-tracking')}
          icon="pin_drop"
          label="Lot Tracking"
          isCollapsed={isCollapsed}
        />
        <NavLink
          isActive={currentView === 'inventory'}
          onClick={() => onNavigate('inventory')}
          icon="inventory_2"
          label="Inventory"
          isCollapsed={isCollapsed}
        />
        <NavLink
          isActive={currentView === 'haccp'}
          onClick={() => onNavigate('haccp')}
          icon="health_and_safety"
          label="HACCP Logs"
          isCollapsed={isCollapsed}
        />
        <NavLink
          isActive={currentView === 'calendar'}
          onClick={() => onNavigate('calendar')}
          icon="calendar_month"
          label="Calendar"
          isCollapsed={isCollapsed}
        />
        <NavLink
          isActive={currentView === 'reports'}
          onClick={() => onNavigate('reports')}
          icon="assessment"
          label="Reports"
          isCollapsed={isCollapsed}
        />
        <NavLink
          isActive={currentView === 'settings'}
          onClick={() => onNavigate('settings')}
          icon="settings"
          label="Settings"
          isCollapsed={isCollapsed}
        />
      </nav>
    </aside>
    </>
  );
};
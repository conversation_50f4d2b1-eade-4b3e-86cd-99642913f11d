

import React, { useState, useEffect, useMemo } from 'react';
import { db } from '../firebase/config';
import { collection, onSnapshot, query, orderBy, addDoc, updateDoc, doc, QuerySnapshot, DocumentData } from 'firebase/firestore';
import { Location, HACCPEvent } from '../types';
import { MapPinIcon, PlusIcon, CubeIcon, EyeIcon } from './IconComponents';
import { FreezerLayoutModal } from './FreezerLayoutModal';
import { docToPlainObject } from '../utils/firestoreUtils';

interface LocationsViewProps {
    events: HACCPEvent[];
}

export const LocationsView: React.FC<LocationsViewProps> = ({ events }) => {
    const [locations, setLocations] = useState<Location[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [isLayoutModalOpen, setIsLayoutModalOpen] = useState(false);
    const [selectedLocationForLayout, setSelectedLocationForLayout] = useState<Location | null>(null);
    
    // Form state
    const [editingId, setEditingId] = useState<string | null>(null);
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');

    const inventoryByLocation = useMemo(() => {
        const inventoryMap: { [locationName: string]: { [product: string]: number } } = {};

        [...events].sort((a, b) => {
            const timeA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
            const timeB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
            return timeA - timeB;
        }).forEach(event => {
            if (!event.product) return;
            const prod = event.product;
            
            switch (event.eventType) {
                case 'receiving':
                    if(event.location) {
                        if (!inventoryMap[event.location]) inventoryMap[event.location] = {};
                        if (!inventoryMap[event.location][prod]) inventoryMap[event.location][prod] = 0;
                        inventoryMap[event.location][prod] += event.quantity || 0;
                    }
                    break;
                case 'sales':
                case 'disposal':
                     // This simple logic assumes a location is specified for the outgoing product.
                     // A more robust system would trace by batch number.
                    if (event.location && inventoryMap[event.location]?.[prod]) {
                        inventoryMap[event.location][prod] -= event.quantity || 0;
                    }
                    break;
                case 'relocation':
                    const qty = event.quantity || 0;
                    if (event.fromLocation && inventoryMap[event.fromLocation]?.[prod]) {
                        inventoryMap[event.fromLocation][prod] -= qty;
                    }
                    if (event.location) { // 'location' is the 'to' location
                        if (!inventoryMap[event.location]) inventoryMap[event.location] = {};
                        if (!inventoryMap[event.location][prod]) inventoryMap[event.location][prod] = 0;
                        inventoryMap[event.location][prod] += qty;
                    }
                    break;
            }
        });
        
        // Clean up zero or negative quantities
        Object.keys(inventoryMap).forEach(loc => {
            Object.keys(inventoryMap[loc]).forEach(prod => {
                if (inventoryMap[loc][prod] <= 0.01) {
                    delete inventoryMap[loc][prod];
                }
            });
        });

        return inventoryMap;

    }, [events]);

    useEffect(() => {
        try {
            const q = query(collection(db, "locations"), orderBy("name"));
            const unsubscribe = onSnapshot(q, (querySnapshot: QuerySnapshot<DocumentData>) => {
                const locationsList: Location[] = querySnapshot.docs.map(doc => docToPlainObject<Location>(doc));
                setLocations(locationsList);
                setLoading(false);
            }, (err) => {
                console.error(err);
                setError("Failed to fetch locations.");
                setLoading(false);
            });
            return () => unsubscribe();
        } catch(e) {
            console.error(e);
            setError("Failed to initialize Firebase for locations.");
            setLoading(false);
        }
    }, []);

    const resetForm = () => {
        setEditingId(null);
        setName('');
        setDescription('');
        setIsFormOpen(false);
    };

    const handleEdit = (location: Location) => {
        setEditingId(location.id);
        setName(location.name);
        setDescription(location.description || '');
        setIsFormOpen(true);
    };

    const handleOpenLayout = (location: Location) => {
        setSelectedLocationForLayout(location);
        setIsLayoutModalOpen(true);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!name) {
            alert("Location name is required.");
            return;
        }

        const locationData = { name, description };

        try {
            if (editingId) {
                const locationRef = doc(db, "locations", editingId);
                await updateDoc(locationRef, locationData);
            } else {
                await addDoc(collection(db, "locations"), locationData);
            }
            resetForm();
        } catch (error) {
            console.error("Error saving location: ", error);
            alert("Failed to save location.");
        }
    };

    return (
        <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 w-full space-y-6">
            <header className="flex items-center justify-between pb-4 border-b border-gray-200">
                <div className="flex items-center">
                    <div className="bg-cyan-100 text-cyan-600 p-2 rounded-lg mr-4">
                        <MapPinIcon className="h-6 w-6" />
                    </div>
                    <h1 className="text-2xl font-bold text-gray-800">Location Management & Inventory</h1>
                </div>
                <button onClick={() => { resetForm(); setIsFormOpen(true); }} className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-blue-700">
                    <PlusIcon className="h-5 w-5 mr-2" />
                    Add Location
                </button>
            </header>

            {isFormOpen && (
                 <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                    <h2 className="text-xl font-semibold mb-4 text-gray-700">{editingId ? 'Edit Location' : 'Add New Location'}</h2>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <label htmlFor="name" className="block text-sm font-medium text-gray-700">Location Name</label>
                            <input type="text" id="name" value={name} onChange={e => setName(e.target.value)} required className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                        </div>
                        <div>
                            <label htmlFor="description" className="block text-sm font-medium text-gray-700">Description</label>
                            <textarea id="description" value={description} onChange={e => setDescription(e.target.value)} rows={3} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"></textarea>
                        </div>
                        <div className="flex justify-end space-x-3">
                            <button type="button" onClick={resetForm} className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">Cancel</button>
                            <button type="submit" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">{editingId ? 'Update Location' : 'Save Location'}</button>
                        </div>
                    </form>
                </div>
            )}

            {loading && <p>Loading locations...</p>}
            {error && <p className="text-red-500">{error}</p>}
            {!loading && !error && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {locations.length === 0 ? <p className="text-center text-gray-500 py-4 lg:col-span-2">No locations added yet.</p> :
                        locations.map(location => {
                            const inventory = inventoryByLocation[location.name] || {};
                            const productCount = Object.keys(inventory).length;
                             return (
                                <div key={location.id} className="p-4 border rounded-lg bg-white hover:bg-gray-50 flex flex-col">
                                    <div className="flex justify-between items-start">
                                        <div>
                                            <h3 className="font-semibold text-lg text-gray-800">{location.name}</h3>
                                            <p className="text-sm text-gray-600">{location.description}</p>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <button onClick={() => handleOpenLayout(location)} className="text-gray-500 hover:text-blue-600 p-1" title="View Layout">
                                                <EyeIcon className="h-5 w-5"/>
                                            </button>
                                            <button onClick={() => handleEdit(location)} className="text-blue-600 hover:text-blue-800 text-sm font-medium">Edit</button>
                                        </div>
                                    </div>
                                    <div className="mt-4 pt-4 border-t flex-grow">
                                        <h4 className="font-semibold text-sm text-gray-700 mb-2 flex items-center"><CubeIcon className="w-4 h-4 mr-2"/> Current Inventory</h4>
                                        {productCount > 0 ? (
                                            <div className="grid grid-cols-2 gap-x-4 gap-y-1">
                                                {Object.entries(inventory).map(([product, quantity]) => (
                                                    <div key={product} className="flex justify-between text-sm">
                                                        <span className="text-gray-600 truncate" title={product}>{product}</span>
                                                        <span className="font-bold text-gray-800 flex-shrink-0 ml-2">{(quantity as number).toFixed(2)} lbs</span>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <p className="text-sm text-gray-500">No inventory tracked at this location.</p>
                                        )}
                                    </div>
                                </div>
                            )
                        })
                    }
                </div>
            )}
            {isLayoutModalOpen && selectedLocationForLayout && (
                <FreezerLayoutModal
                    isOpen={isLayoutModalOpen}
                    onClose={() => setIsLayoutModalOpen(false)}
                    location={selectedLocationForLayout}
                    inventory={inventoryByLocation[selectedLocationForLayout.name] || {}}
                />
            )}
        </div>
    );
};
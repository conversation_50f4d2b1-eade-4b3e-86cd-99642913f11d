
import React from 'react';
import { ConversationTurn } from '../types';

interface ConversationLogProps {
  turns: ConversationTurn[];
}

export const ConversationLog: React.FC<ConversationLogProps> = ({ turns }) => (
  <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl shadow-lg p-6 border border-gray-700 h-64 overflow-y-auto flex flex-col-reverse">
    <div className="space-y-4">
      {turns.map((turn, index) => (
        <div key={index} className={`flex flex-col ${turn.speaker === 'user' ? 'items-start' : 'items-end'}`}>
          <div className={`rounded-lg px-4 py-2 max-w-sm ${turn.speaker === 'user' ? 'bg-blue-600' : 'bg-gray-600'}`}>
            <p className="text-sm">{turn.text}</p>
          </div>
          <p className="text-xs text-gray-400 mt-1 capitalize">{turn.speaker}</p>
        </div>
      ))}
       {turns.length === 0 && (
        <div className="flex items-center justify-center h-full">
            <p className="text-gray-400">Conversation will appear here.</p>
        </div>
       )}
    </div>
  </div>
);

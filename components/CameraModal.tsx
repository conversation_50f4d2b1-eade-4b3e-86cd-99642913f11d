import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { GoogleGenAI, Type } from '@google/genai';
import { HACCPEvent, Species, Vendor, Location } from '../types';
import { XIcon, CameraIcon, TrashIcon } from './IconComponents';

interface CameraModalProps {
    species: Species[];
    vendors: Vendor[];
    locations: Location[];
    onClose: () => void;
    onSave: (events: Partial<HACCPEvent>[]) => void;
}

type ModalStep = 'camera' | 'preview' | 'loading' | 'confirm';

export const CameraModal: React.FC<CameraModalProps> = ({ species, vendors, locations, onClose, onSave }) => {
    const [step, setStep] = useState<ModalStep>('camera');
    const [capturedImage, setCapturedImage] = useState<string | null>(null);
    const [extractedEvents, setExtractedEvents] = useState<Partial<HACCPEvent>[]>([]);
    const [error, setError] = useState<string | null>(null);
    const videoRef = useRef<HTMLVideoElement>(null);
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const streamRef = useRef<MediaStream | null>(null);

    const stopCamera = useCallback(() => {
        if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
            streamRef.current = null;
        }
    }, []);
    
    useEffect(() => {
        const startCamera = async () => {
            if (step === 'camera' && !streamRef.current) {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: 'environment' } });
                    streamRef.current = stream;
                    if (videoRef.current) {
                        videoRef.current.srcObject = stream;
                    }
                } catch (err) {
                    console.error("Error accessing camera:", err);
                    setError("Could not access camera. Please check permissions.");
                }
            }
        };

        startCamera();
        
        return () => {
            stopCamera();
        };
    }, [step, stopCamera]);


    const handleTakePicture = () => {
        if (videoRef.current && canvasRef.current) {
            const video = videoRef.current;
            const canvas = canvasRef.current;
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const context = canvas.getContext('2d');
            if (context) {
                context.drawImage(video, 0, 0, canvas.width, canvas.height);
                const dataUrl = canvas.toDataURL('image/jpeg');
                setCapturedImage(dataUrl);
                setStep('preview');
                stopCamera();
            }
        }
    };

    const handleUsePicture = async () => {
        if (!capturedImage) return;
        setStep('loading');
        setError(null);
        try {
            const ai = new GoogleGenAI({ apiKey: process.env.API_KEY as string });
            const base64Data = capturedImage.split(',')[1];
            
            const imagePart = { inlineData: { mimeType: 'image/jpeg', data: base64Data } };
            
            const response = await ai.models.generateContent({
                model: 'gemini-2.5-flash',
                contents: { parts: [imagePart] },
                config: {
                    systemInstruction: `You are an intelligent data entry assistant for a seafood company. Analyze this image of a document (e.g., an invoice, receiving log, or inventory sheet). Extract all distinct line items as separate events. Structure your response as a JSON array of objects, where each object represents one event. Use the provided schema. Extract all available columns of data, such as product name, quantity, supplier, batch number, location, temperature, product form, and unit. Map them to the fields in the provided JSON schema. Infer the 'eventType' from the document's title or content (e.g., 'receiving' for invoices, 'inventory' for stock takes). If a date is present, use it in YYYY-MM-DD format; otherwise, omit the date field. For quantities, provide a number. Be precise. If a value for a field isn't in the document, omit that key from the object. Your output MUST be ONLY the JSON array.`,
                    responseMimeType: 'application/json',
                    responseSchema: {
                        type: Type.ARRAY,
                        items: {
                            type: Type.OBJECT,
                            properties: {
                                eventType: { type: Type.STRING, enum: ['receiving', 'inventory', 'disposal', 'sales', 'sanitation'] },
                                product: { type: Type.STRING, description: 'The name of the seafood species.' },
                                productForm: { type: Type.STRING, description: 'The form of the product, e.g., Fillet, Whole.' },
                                quantity: { type: Type.NUMBER, description: 'The quantity of the product.' },
                                unit: { type: Type.STRING, description: 'The unit of measurement, e.g., lbs.' },
                                supplier: { type: Type.STRING, description: 'The name of the vendor or supplier.' },
                                location: { type: Type.STRING, description: 'The storage location.' },
                                date: { type: Type.STRING, description: 'The date of the event in YYYY-MM-DD format.' },
                                batchNumber: { type: Type.STRING, description: 'The batch or lot number.' },
                                temperature: { type: Type.NUMBER, description: 'The temperature in Fahrenheit.' },
                                notes: { type: Type.STRING, description: 'Any other relevant notes from the document.' },
                            },
                        },
                    },
                },
            });
            
            const parsedEvents = JSON.parse(response.text);
            setExtractedEvents(parsedEvents);
            setStep('confirm');

        } catch (err: any) {
            console.error("Error analyzing image:", err);
            if (err.message?.includes('does not have permission') || err.message?.includes('not found')) {
                setError("API Key Error. Please close and re-select your API key from the main screen.");
            } else {
                setError("AI analysis failed. Please try a clearer picture.");
            }
            setStep('preview');
        }
    };

    const handleDataChange = (index: number, field: keyof HACCPEvent, value: string | number) => {
        const updatedEvents = [...extractedEvents];
        updatedEvents[index] = { ...updatedEvents[index], [field]: value };
        setExtractedEvents(updatedEvents);
    };

    const removeEvent = (index: number) => {
        setExtractedEvents(prev => prev.filter((_, i) => i !== index));
    };

    const { newSpecies, newVendors, newLocations } = useMemo(() => {
        const existingSpecies = new Set(species.map(s => s.name.toLowerCase()));
        const existingVendors = new Set(vendors.map(v => v.name.toLowerCase()));
        const existingLocations = new Set(locations.map(l => l.name.toLowerCase()));

        const newSpecies = new Set<string>();
        const newVendors = new Set<string>();
        const newLocations = new Set<string>();

        extractedEvents.forEach(event => {
            if (event.product && !existingSpecies.has(event.product.toLowerCase())) {
                newSpecies.add(event.product);
            }
            if (event.supplier && !existingVendors.has(event.supplier.toLowerCase())) {
                newVendors.add(event.supplier);
            }
            if (event.location && !existingLocations.has(event.location.toLowerCase())) {
                newLocations.add(event.location);
            }
        });

        return { 
            newSpecies: Array.from(newSpecies), 
            newVendors: Array.from(newVendors), 
            newLocations: Array.from(newLocations) 
        };
    }, [extractedEvents, species, vendors, locations]);

    const renderContent = () => {
        switch (step) {
            case 'camera':
                return (
                    <div className="relative w-full h-full bg-black flex flex-col items-center justify-center">
                        <video ref={videoRef} autoPlay playsInline className="w-full h-full object-contain" />
                        <button onClick={handleTakePicture} className="absolute bottom-8 bg-white rounded-full p-4 shadow-lg">
                            <CameraIcon className="h-8 w-8 text-gray-800" />
                        </button>
                    </div>
                );
            case 'preview':
                return (
                    <div className="relative w-full h-full bg-black flex flex-col items-center justify-center">
                        {capturedImage && <img src={capturedImage} alt="Captured" className="w-full h-full object-contain" />}
                         {error && <p className="absolute top-4 bg-red-500 text-white p-2 rounded">{error}</p>}
                        <div className="absolute bottom-8 flex space-x-8">
                            <button onClick={() => setStep('camera')} className="bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold">Retake</button>
                            <button onClick={handleUsePicture} className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold">Use Picture</button>
                        </div>
                    </div>
                );
            case 'loading':
                return (
                    <div className="flex flex-col items-center justify-center h-full text-white">
                         <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white"></div>
                        <p className="mt-4 text-lg">Analyzing Document...</p>
                    </div>
                );
            case 'confirm':
                const hasNewItems = newSpecies.length > 0 || newVendors.length > 0 || newLocations.length > 0;
                return (
                    <div className="p-6 flex-grow overflow-y-auto">
                        <h3 className="text-xl font-bold mb-4 text-white">Confirm Extracted Data</h3>
                        {hasNewItems && (
                            <div className="bg-blue-900/50 border border-blue-700 p-3 rounded-lg mb-4 text-sm">
                                <p className="font-semibold text-blue-300">New items detected!</p>
                                <p className="text-blue-400">These will be added to your database upon saving:</p>
                                <ul className="list-disc list-inside text-xs mt-1">
                                    {newSpecies.length > 0 && <li><strong>Species:</strong> {newSpecies.join(', ')}</li>}
                                    {newVendors.length > 0 && <li><strong>Vendors:</strong> {newVendors.join(', ')}</li>}
                                    {newLocations.length > 0 && <li><strong>Locations:</strong> {newLocations.join(', ')}</li>}
                                </ul>
                            </div>
                        )}
                        <div className="space-y-4">
                            {extractedEvents.map((event, index) => {
                                const isNewProduct = event.product && newSpecies.includes(event.product);
                                const isNewSupplier = event.supplier && newVendors.includes(event.supplier);
                                const isNewLocation = event.location && newLocations.includes(event.location);

                                return (
                                <div key={index} className="bg-gray-800 p-4 rounded-lg space-y-2 relative">
                                    <button onClick={() => removeEvent(index)} className="absolute top-2 right-2 text-gray-400 hover:text-red-500"><TrashIcon className="h-5 w-5"/></button>
                                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                                        <div className="relative">
                                            <label className="text-xs text-gray-400">Event</label>
                                            <input value={event.eventType || ''} onChange={e => handleDataChange(index, 'eventType', e.target.value)} placeholder="Event Type" className="bg-gray-700 p-2 rounded w-full"/>
                                        </div>
                                        <div className="relative">
                                            <label className="text-xs text-gray-400">Product</label>
                                            <input value={event.product || ''} onChange={e => handleDataChange(index, 'product', e.target.value)} placeholder="Product" className={`bg-gray-700 p-2 rounded w-full ${isNewProduct ? 'border border-green-500' : 'border-transparent'} border-2`}/>
                                            {isNewProduct && <span className="absolute top-0 right-0 mt-1 mr-1 px-2 py-0.5 bg-green-500 text-white text-xs rounded-full">New</span>}
                                        </div>
                                        <div className="relative">
                                            <label className="text-xs text-gray-400">Quantity</label>
                                            <input value={event.quantity || ''} onChange={e => handleDataChange(index, 'quantity', e.target.value)} placeholder="Quantity" type="number" className="bg-gray-700 p-2 rounded w-full"/>
                                        </div>
                                        <div className="relative">
                                            <label className="text-xs text-gray-400">Supplier</label>
                                            <input value={event.supplier || ''} onChange={e => handleDataChange(index, 'supplier', e.target.value)} placeholder="Supplier" className={`bg-gray-700 p-2 rounded w-full ${isNewSupplier ? 'border border-green-500' : 'border-transparent'} border-2`}/>
                                            {isNewSupplier && <span className="absolute top-0 right-0 mt-1 mr-1 px-2 py-0.5 bg-green-500 text-white text-xs rounded-full">New</span>}
                                        </div>
                                        <div className="relative">
                                            <label className="text-xs text-gray-400">Location</label>
                                            <input value={event.location || ''} onChange={e => handleDataChange(index, 'location', e.target.value)} placeholder="Location" className={`bg-gray-700 p-2 rounded w-full ${isNewLocation ? 'border border-green-500' : 'border-transparent'} border-2`}/>
                                            {isNewLocation && <span className="absolute top-0 right-0 mt-1 mr-1 px-2 py-0.5 bg-green-500 text-white text-xs rounded-full">New</span>}
                                        </div>
                                        <div className="relative">
                                            <label className="text-xs text-gray-400">Date</label>
                                            <input value={event.date || ''} onChange={e => handleDataChange(index, 'date', e.target.value)} placeholder="Date" type="date" className="bg-gray-700 p-2 rounded w-full"/>
                                        </div>
                                        <div className="relative md:col-span-3">
                                            <label className="text-xs text-gray-400">Notes</label>
                                            <input value={event.notes || ''} onChange={e => handleDataChange(index, 'notes', e.target.value)} placeholder="Notes..." className="bg-gray-700 p-2 rounded w-full"/>
                                        </div>
                                    </div>
                                </div>
                                );
                            })}
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };
    
    return (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4">
            <div className="bg-gray-900 text-white rounded-2xl shadow-2xl w-full max-w-2xl flex flex-col h-[90vh] border border-gray-700">
                <header className="flex items-center justify-between p-4 border-b border-gray-700 flex-shrink-0">
                    <h2 className="text-xl font-bold">Document Scanner</h2>
                    <button onClick={onClose} className="text-gray-400 hover:text-white">
                        <XIcon className="h-6 w-6" />
                    </button>
                </header>
                <main className="flex-grow overflow-hidden flex flex-col">
                    {renderContent()}
                </main>
                {step === 'confirm' && (
                    <footer className="p-4 border-t border-gray-700 flex justify-end flex-shrink-0">
                        <button onClick={() => onSave(extractedEvents)} className="bg-green-600 text-white px-6 py-2 rounded-lg font-semibold">
                            Save {extractedEvents.length} Items
                        </button>
                    </footer>
                )}
                 <canvas ref={canvasRef} style={{ display: 'none' }} />
            </div>
        </div>
    );
};


import React from 'react';

interface StatusIndicatorProps {
  status: string;
  isRecording: boolean;
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({ status, isRecording }) => (
  <div className="flex items-center justify-center space-x-3 p-4 bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700">
    {isRecording && (
      <span className="relative flex h-3 w-3">
        <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
        <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
      </span>
    )}
    <p className="text-lg font-medium text-gray-300">{status}</p>
  </div>
);

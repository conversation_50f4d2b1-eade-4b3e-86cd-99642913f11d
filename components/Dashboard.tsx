import React, { useState, useMemo } from 'react';
import { EmailContext, HACCPEvent, Species, Vendor, Location, EventType } from '../types';
import { PackageIcon, ShoppingCartIcon, TrashIcon, ClipboardIcon, ChartBarIcon, PencilSquareIcon, SparklesIcon, ThermometerIcon, ArchiveBoxIcon, FunnelIcon, UserCircleIcon, ClipboardDocumentListIcon, ChartPieIcon, TruckIcon } from './IconComponents';
import { InventoryDisplay } from './InventoryDisplay';

interface DashboardProps {
    events: HACCPEvent[];
    species: Species[];
    vendors: Vendor[];
    locations: Location[];
    onEditEvent: (event: HACCPEvent) => void;
    onDraftEmail: (context: EmailContext) => void;
    onUpdateEventField: (eventId: string, field: keyof HACCPEvent, value: any) => Promise<boolean>;
}

const eventTypeConfig: { [key in EventType]: { icon: React.ReactNode; label: string; color: string; tailwindColor: string } } = {
    receiving: { icon: <PackageIcon />, label: 'Receiving', color: '#3B82F6', tailwindColor: 'blue' },
    sales: { icon: <ShoppingCartIcon />, label: 'Sales', color: '#10B981', tailwindColor: 'green' },
    disposal: { icon: <TrashIcon />, label: 'Disposal', color: '#EF4444', tailwindColor: 'red' },
    're-sealing': { icon: <ClipboardIcon />, label: 'Re-sealing', color: '#8B5CF6', tailwindColor: 'purple' },
    relocation: { icon: <TruckIcon />, label: 'Relocation', color: '#6366F1', tailwindColor: 'indigo' },
    sanitation: { icon: <SparklesIcon />, label: 'Sanitation', color: '#14B8A6', tailwindColor: 'teal' },
    'thermometer-calibration': { icon: <ThermometerIcon />, label: 'Calibration', color: '#F97316', tailwindColor: 'orange' },
    inventory: { icon: <ArchiveBoxIcon />, label: 'Inventory', color: '#F59E0B', tailwindColor: 'yellow' },
    'employee-training': { icon: <UserCircleIcon />, label: 'Training', color: '#06B6D4', tailwindColor: 'cyan' },
};

const EventTypeDisplay = ({ eventType }: { eventType: EventType }) => {
    const config = eventTypeConfig[eventType] || { icon: null, label: eventType, tailwindColor: 'gray' };
    const { icon, label, tailwindColor } = config;

    return (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${tailwindColor}-100 text-${tailwindColor}-800`}>
            <div className={`w-4 h-4 mr-1.5 text-${tailwindColor}-600`}>{icon}</div>
            {label}
        </span>
    )
}

const KpiCard: React.FC<{ title: string; value: string; icon: React.ReactNode; }> = ({ title, value, icon }) => (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-center">
        <div className="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
            {icon}
        </div>
        <div>
            <p className="text-sm text-gray-500">{title}</p>
            <p className="text-2xl font-bold text-gray-800">{value}</p>
        </div>
    </div>
);

const EventPieChart: React.FC<{ data: { type: EventType; count: number; percentage: number; color: string }[] }> = ({ data }) => {
    const radius = 60;
    const circumference = 2 * Math.PI * radius;
    let accumulatedOffset = 0;

    return (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 flex flex-col md:flex-row items-center h-full">
            <div className="relative w-40 h-40">
                <svg viewBox="0 0 140 140" className="transform -rotate-90">
                    {data.map(({ percentage, color }, index) => {
                        const strokeDashoffset = circumference - (percentage / 100) * circumference;
                        const rotation = (accumulatedOffset / 100) * 360;
                        accumulatedOffset += percentage;
                        return (
                            <circle
                                key={index}
                                r={radius}
                                cx="70"
                                cy="70"
                                fill="transparent"
                                stroke={color}
                                strokeWidth="20"
                                strokeDasharray={circumference}
                                strokeDashoffset={strokeDashoffset}
                                style={{ transform: `rotate(${rotation}deg)`, transformOrigin: '70px 70px' }}
                            />
                        );
                    })}
                </svg>
            </div>
            <div className="mt-4 md:mt-0 md:ml-6 flex-grow">
                <h3 className="font-semibold text-gray-700 mb-2">Event Distribution</h3>
                <ul className="space-y-1 text-sm">
                    {data.map(({ type, count, percentage, color }) => (
                        <li key={type} className="flex items-center justify-between">
                            <div className="flex items-center">
                                <span className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: color }}></span>
                                <span className="text-gray-600 capitalize">{eventTypeConfig[type]?.label || type}</span>
                            </div>
                            <span className="font-semibold text-gray-800">{percentage.toFixed(1)}% ({count})</span>
                        </li>
                    ))}
                </ul>
            </div>
        </div>
    );
};


const EventBarChart: React.FC<{ data: { date: string; count: number }[] }> = ({ data }) => {
    const maxCount = Math.max(...data.map(d => d.count), 0) || 1;
    const labels = data.map(d => new Date(d.date + 'T00:00:00').toLocaleDateString('en-US', { weekday: 'short' }));

    return (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 h-full">
            <h3 className="font-semibold text-gray-700 mb-4">Activity Last 7 Days</h3>
            <div className="flex justify-around items-end h-48 space-x-2">
                {data.map((item, index) => (
                    <div key={index} className="flex-1 flex flex-col items-center justify-end">
                        <div
                            className="w-full bg-blue-500 rounded-t-sm"
                            style={{ height: `${(item.count / maxCount) * 100}%` }}
                            title={`${item.count} events on ${labels[index]}`}
                        ></div>
                        <span className="text-xs text-gray-500 mt-1">{labels[index]}</span>
                    </div>
                ))}
            </div>
        </div>
    );
};


const initialFilters = {
    startDate: '',
    endDate: '',
    eventTypes: [] as string[],
    species: [] as string[],
    vendors: [] as string[],
    locations: [] as string[],
    batchNumber: '',
};

export const Dashboard: React.FC<DashboardProps> = ({ events, species, vendors, locations, onEditEvent, onDraftEmail, onUpdateEventField }) => {
    const [filters, setFilters] = useState(initialFilters);
    const [showFilters, setShowFilters] = useState(false);

    const handleFilterChange = (filterName: keyof typeof initialFilters, value: any) => {
        setFilters(prev => ({ ...prev, [filterName]: value }));
    };
    
    const handleMultiSelectChange = (filterName: 'eventTypes' | 'species' | 'vendors' | 'locations', options: HTMLOptionsCollection) => {
        const value = Array.from(options).filter(option => option.selected).map(option => option.value);
        handleFilterChange(filterName, value);
    };

    const filteredEvents = useMemo(() => {
        return events.filter(event => {
            if (filters.startDate && event.date < filters.startDate) return false;
            if (filters.endDate && event.date > filters.endDate) return false;
            if (filters.eventTypes.length > 0 && !filters.eventTypes.includes(event.eventType)) return false;
            if (filters.species.length > 0 && (!event.product || !filters.species.includes(event.product))) return false;
            if (filters.vendors.length > 0 && (!event.supplier || !filters.vendors.includes(event.supplier))) return false;
            if (filters.locations.length > 0 && (!event.location || !filters.locations.includes(event.location))) return false;
            if (filters.batchNumber && (!event.batchNumber || !event.batchNumber.toLowerCase().includes(filters.batchNumber.toLowerCase()))) return false;
            return true;
        });
    }, [events, filters]);
    
    const eventTypes = [...new Set(events.map(e => e.eventType))];
    const productRequiredEventTypes: EventType[] = ['receiving', 'sales', 'disposal', 're-sealing', 'inventory', 'relocation'];

    const kpiData = useMemo(() => {
        const today = new Date();
        const oneWeekAgo = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 7);
        const oneMonthAgo = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 30);
        
        const eventsLastWeek = events.filter(e => new Date(e.date) >= oneWeekAgo);
        const receivingLastMonth = events.filter(e => e.eventType === 'receiving' && new Date(e.date) >= oneMonthAgo);

        const eventTypeCounts = events.reduce((acc, event) => {
            acc[event.eventType] = (acc[event.eventType] || 0) + 1;
            return acc;
        }, {} as { [key: string]: number });
        
        const mostFrequent = Object.entries(eventTypeCounts).sort((a, b) => (b[1] as number) - (a[1] as number))[0];

        return {
            totalEventsLastWeek: eventsLastWeek.length,
            totalWeightReceived: receivingLastMonth.reduce((sum, e) => sum + (e.quantity || 0), 0),
            mostFrequentEventType: mostFrequent ? eventTypeConfig[mostFrequent[0] as EventType]?.label : 'N/A',
        };
    }, [events]);

    const chartData = useMemo(() => {
        // Pie Chart Data
        const eventTypeCounts = events.reduce((acc, event) => {
            acc[event.eventType] = (acc[event.eventType] || 0) + 1;
            return acc;
        }, {} as { [key: string]: number });

        const totalEvents = events.length;
        const pieChartData = Object.entries(eventTypeCounts)
            .map(([type, countValue]) => {
                const count = countValue as number;
                return {
                    type: type as EventType,
                    count,
                    percentage: totalEvents > 0 ? (count / totalEvents) * 100 : 0,
                    color: eventTypeConfig[type as EventType]?.color || '#9CA3AF',
                };
            })
            .sort((a, b) => b.count - a.count);

        // Bar Chart Data
        const last7Days = Array.from({ length: 7 }, (_, i) => {
            const d = new Date();
            d.setDate(d.getDate() - i);
            return d.toISOString().split('T')[0];
        }).reverse();
        
        const barChartData = last7Days.map(date => {
            const count = events.filter(e => e.date === date).length;
            return { date, count };
        });

        return { pieChartData, barChartData };
    }, [events]);


    return (
        <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 max-w-6xl w-full mx-auto space-y-8">
            <InventoryDisplay events={events} />

            {/* KPIs */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <KpiCard title="Events This Week" value={kpiData.totalEventsLastWeek.toString()} icon={<ClipboardDocumentListIcon className="h-6 w-6" />} />
                <KpiCard title="Receiving This Month" value={`${kpiData.totalWeightReceived.toFixed(1)} lbs`} icon={<PackageIcon className="h-6 w-6" />} />
                <KpiCard title="Top Activity" value={kpiData.mostFrequentEventType} icon={<SparklesIcon className="h-6 w-6" />} />
            </div>
            
            {/* Visualizations */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <EventPieChart data={chartData.pieChartData} />
                <EventBarChart data={chartData.barChartData} />
            </div>

            <div>
                 <header className="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
                    <div className="flex items-center">
                        <div className="bg-blue-100 text-blue-600 p-2 rounded-lg mr-4">
                            <ChartBarIcon className="h-6 w-6" />
                        </div>
                        <h1 className="text-2xl font-bold text-gray-800">Detailed Event History</h1>
                    </div>
                </header>

                {/* Filter Section */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
                    <button onClick={() => setShowFilters(!showFilters)} className="w-full flex justify-between items-center text-left font-semibold text-gray-700">
                        <span className="flex items-center"><FunnelIcon className="h-5 w-5 mr-2" /> Filter Events</span>
                        <span className="transform transition-transform">{showFilters ? '▲' : '▼'}</span>
                    </button>
                    {showFilters && (
                        <div className="mt-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            {/* Date Filters */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Start Date</label>
                                <input type="date" value={filters.startDate} onChange={e => handleFilterChange('startDate', e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm text-sm" />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">End Date</label>
                                <input type="date" value={filters.endDate} onChange={e => handleFilterChange('endDate', e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm text-sm" />
                            </div>
                            {/* Batch Number */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Batch Number</label>
                                <input type="text" placeholder="Search batch..." value={filters.batchNumber} onChange={e => handleFilterChange('batchNumber', e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm text-sm" />
                            </div>
                            {/* Event Type */}
                             <div>
                                <label className="block text-sm font-medium text-gray-700">Event Type</label>
                                <select multiple value={filters.eventTypes} onChange={e => handleMultiSelectChange('eventTypes', e.target.options)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm text-sm h-24">
                                    {eventTypes.map(type => <option key={type} value={type}>{eventTypeConfig[type as EventType]?.label || type}</option>)}
                                </select>
                            </div>
                            {/* Species */}
                             <div>
                                <label className="block text-sm font-medium text-gray-700">Species</label>
                                <select multiple value={filters.species} onChange={e => handleMultiSelectChange('species', e.target.options)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm text-sm h-24">
                                    {species.map(s => <option key={s.id} value={s.name}>{s.name}</option>)}
                                </select>
                            </div>
                            {/* Vendors */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Vendors</label>
                                <select multiple value={filters.vendors} onChange={e => handleMultiSelectChange('vendors', e.target.options)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm text-sm h-24">
                                    {vendors.map(v => <option key={v.id} value={v.name}>{v.name}</option>)}
                                </select>
                            </div>
                            {/* Locations */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Locations</label>
                                <select multiple value={filters.locations} onChange={e => handleMultiSelectChange('locations', e.target.options)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm text-sm h-24">
                                    {locations.map(l => <option key={l.id} value={l.name}>{l.name}</option>)}
                                </select>
                            </div>
                            <div className="flex items-end">
                                <button onClick={() => setFilters(initialFilters)} className="w-full bg-gray-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-gray-700 text-sm">Reset Filters</button>
                            </div>
                        </div>
                    )}
                </div>

                {filteredEvents.length === 0 ? (
                    <p className="text-center text-gray-500 py-8">No events match the current filters.</p>
                ) : (
                    <>
                        {/* Mobile Card View */}
                        <div className="md:hidden space-y-3">
                            {filteredEvents.map((event) => {
                                const isProductMissing = productRequiredEventTypes.includes(event.eventType) && !event.product;
                                return (
                                <div key={event.id} className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                                    <div className="flex justify-between items-start mb-2">
                                        <EventTypeDisplay eventType={event.eventType} />
                                        <div className="text-right text-xs text-gray-500">
                                            <p>{event.date}</p>
                                            <p>{event.time}</p>
                                        </div>
                                    </div>
                                    <div className="font-semibold text-gray-800">
                                    {isProductMissing ? (
                                        <select
                                            className="w-full p-1 border border-gray-300 rounded-md shadow-sm text-sm focus:ring-indigo-500 focus:border-indigo-500"
                                            onChange={(e) => {
                                                if (e.target.value) onUpdateEventField(event.id, 'product', e.target.value);
                                            }}
                                            defaultValue=""
                                        >
                                            <option value="" disabled>-- Select Product --</option>
                                            {species.map(s => <option key={s.id} value={s.name}>{s.name}</option>)}
                                        </select>
                                    ) : (
                                        event.product ? [event.product, event.productForm].filter(Boolean).join(' - ') : event.employeeName || 'N/A'
                                    )}
                                    </div>
                                    <div className="mt-2 pt-2 border-t border-gray-200 grid grid-cols-2 gap-x-4 gap-y-1 text-xs">
                                        {event.quantity && <div><strong className="text-gray-500">Qty:</strong> {event.quantity.toFixed(2)} {event.unit}</div>}
                                        {event.batchNumber && <div className="font-mono"><strong className="text-gray-500">Batch:</strong> {event.batchNumber}</div>}
                                        {(event.supplier || event.fromLocation) && <div><strong className="text-gray-500">From:</strong> {event.supplier || event.fromLocation}</div>}
                                        {event.location && <div><strong className="text-gray-500">To:</strong> {event.location}</div>}
                                        {event.createdBy && <div><strong className="text-gray-500">User:</strong> {event.createdBy}</div>}
                                    </div>
                                     <div className="mt-2 pt-2 border-t border-gray-200 flex justify-end items-center space-x-3">
                                         {event.eventType === 'receiving' && (
                                            <button onClick={() => onDraftEmail({type: 'receiving', event})} className="text-indigo-600 hover:text-indigo-900 text-xs font-semibold">
                                                EMAIL
                                            </button>
                                        )}
                                        <button onClick={() => onEditEvent(event)} className="flex items-center text-blue-600 hover:text-blue-900 text-sm font-semibold">
                                            <PencilSquareIcon className="h-4 w-4 mr-1" />
                                            Edit
                                        </button>
                                    </div>
                                </div>
                            )})}
                        </div>
                        
                        {/* Desktop Table View */}
                        <div className="overflow-x-auto hidden md:block">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Details</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Qty</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Batch #</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Vendor/From</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Location/To</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">User</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {filteredEvents.map((event) => {
                                        const isProductMissing = productRequiredEventTypes.includes(event.eventType) && !event.product;
                                        return (
                                        <tr key={event.id} className="hover:bg-gray-50">
                                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">{event.date} {event.time}</td>
                                            <td className="px-4 py-3 whitespace-nowrap"><EventTypeDisplay eventType={event.eventType} /></td>
                                            <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {isProductMissing ? (
                                                    <select
                                                        className="w-full p-1 border border-gray-300 rounded-md shadow-sm text-sm focus:ring-indigo-500 focus:border-indigo-500"
                                                        onChange={(e) => {
                                                            if (e.target.value) {
                                                                onUpdateEventField(event.id, 'product', e.target.value);
                                                            }
                                                        }}
                                                        defaultValue=""
                                                        aria-label={`Select product for event on ${event.date}`}
                                                    >
                                                        <option value="" disabled>-- Select --</option>
                                                        {species.map(s => (
                                                            <option key={s.id} value={s.name}>{s.name}</option>
                                                        ))}
                                                    </select>
                                                ) : (
                                                    event.product ? [event.product, event.productForm].filter(Boolean).join(' - ') : event.employeeName || 'N/A'
                                                )}
                                            </td>
                                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">{event.quantity ? `${event.quantity.toFixed(2)} ${event.unit}` : 'N/A'}</td>
                                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 font-mono text-xs">{event.batchNumber || 'N/A'}</td>
                                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">{event.supplier || event.fromLocation || 'N/A'}</td>
                                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">{event.location || 'N/A'}</td>
                                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{event.createdBy}</td>
                                            <td className="px-4 py-3 whitespace-nowrap text-sm font-medium">
                                                <div className="flex items-center space-x-2">
                                                    <button onClick={() => onEditEvent(event)} className="text-blue-600 hover:text-blue-900" title="Edit Event">
                                                        <PencilSquareIcon className="h-5 w-5" />
                                                    </button>
                                                    {event.eventType === 'receiving' && (
                                                        <button onClick={() => onDraftEmail({type: 'receiving', event})} className="text-indigo-600 hover:text-indigo-900 text-xs" title="Draft Receiving Email">
                                                            Email
                                                        </button>
                                                    )}
                                                </div>
                                            </td>
                                        </tr>
                                    )})}
                                </tbody>
                            </table>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
};
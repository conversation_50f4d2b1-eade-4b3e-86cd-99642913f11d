import React from 'react';

export const MockDataWarning: React.FC = () => {
    return (
        <div className="p-4 bg-yellow-50 border-l-4 border-yellow-400 text-yellow-800 rounded-r-lg">
            <div className="flex">
                <div className="py-1">
                    <svg className="h-6 w-6 text-yellow-500 mr-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                </div>
                <div>
                    <p className="font-bold">Displaying Sample Data</p>
                    <p className="text-sm">
                        Live data from the TempStick API cannot be fetched directly from the browser due to CORS security restrictions.
                        This component is currently showing pre-loaded sample data.
                    </p>
                    <p className="text-sm mt-1">
                        <strong>Developer Note:</strong> To connect to the live API, a backend proxy is required to handle API requests and add the necessary CORS headers, as described in the documentation.
                    </p>
                </div>
            </div>
        </div>
    );
};

import React, { useState } from 'react';
import { Species } from '../types';

interface AddProductModalProps {
  onClose: () => void;
  onSave: (newSpecies: Partial<Species>) => void;
}

export const AddProductModal: React.FC<AddProductModalProps> = ({ onClose, onSave }) => {
  const [name, setName] = useState('');
  const [category, setCategory] = useState<'Fish' | 'Shellfish' | 'Other'>('Other');
  const [productForms, setProductForms] = useState('');

  const handleSave = () => {
    onSave({
      name,
      category,
      productForms: productForms.split(',').map(s => s.trim()),
    });
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <h2 className="text-2xl font-bold mb-4">Add New Product</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Product Name</label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Category</label>
            <select
              value={category}
              onChange={(e) => setCategory(e.target.value as 'Fish' | 'Shellfish' | 'Other')}
              className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"
            >
              <option value="Fish">Fish</option>
              <option value="Shellfish">Shellfish</option>
              <option value="Other">Other</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Product Forms (comma-separated)</label>
            <input
              type="text"
              value={productForms}
              onChange={(e) => setProductForms(e.target.value)}
              className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"
              placeholder="e.g., Fillet, Whole, Portion"
            />
          </div>
        </div>
        <div className="mt-6 flex justify-end space-x-3">
          <button onClick={onClose} className="bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300">
            Cancel
          </button>
          <button onClick={handleSave} className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            Save Product
          </button>
        </div>
      </div>
    </div>
  );
};
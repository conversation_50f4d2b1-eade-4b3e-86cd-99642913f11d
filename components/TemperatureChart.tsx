import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { TempStickSensor, TempStickReading } from '../types';
import { XIcon } from './IconComponents';
import { fetchReadings } from '../services/tempstickService'; // Import the new service

interface TemperatureChartProps {
  sensor: TempStickSensor;
  onClose: () => void;
}

type TimeRange = 'today' | '24_hours' | 'last_week' | 'last_month';

const timeRangeOptions: { value: TimeRange; label: string }[] = [
    { value: 'today', label: 'Today' },
    { value: '24_hours', label: '24 Hours' },
    { value: 'last_week', label: 'Last Week' },
    { value: 'last_month', label: 'Last Month' },
];

export const TemperatureChart: React.FC<TemperatureChartProps> = ({ sensor, onClose }) => {
    const [readings, setReadings] = useState<TempStickReading[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [timeRange, setTimeRange] = useState<TimeRange>('last_week');

    const loadReadings = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            // Use the new service to fetch readings
            const data = await fetchReadings(sensor.sensor_id, timeRange);
            setReadings(data);
        } catch (err: any) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, [sensor.sensor_id, timeRange]);

    useEffect(() => {
        loadReadings();
    }, [loadReadings]);

    const chartData = useMemo(() => {
        if (readings.length === 0) return null;
    
        const parsedReadings = readings.map(r => ({
            time: new Date(r.sensor_time),
            temp: r.temperature,
        })).sort((a,b) => a.time.getTime() - b.time.getTime());
    
        const temps = parsedReadings.map(r => r.temp);
        const minTemp = Math.min(...temps);
        const maxTemp = Math.max(...temps);
        const tempRange = maxTemp - minTemp;
    
        const times = parsedReadings.map(r => r.time.getTime());
        const minTime = Math.min(...times);
        const maxTime = Math.max(...times);
        const timeRange = maxTime - minTime;
    
        const yPadding = tempRange * 0.1;
        const chartMinTemp = Math.floor(minTemp - yPadding);
        const chartMaxTemp = Math.ceil(maxTemp + yPadding);
    
        const points = parsedReadings.map(r => {
            const x = timeRange === 0 ? 50 : ((r.time.getTime() - minTime) / timeRange) * 100;
            const y = 100 - ((r.temp - chartMinTemp) / (chartMaxTemp - chartMinTemp)) * 100;
            return { x, y, temp: r.temp, time: r.time };
        });
    
        const path = points.map((p, i) => (i === 0 ? 'M' : 'L') + `${p.x} ${p.y}`).join(' ');
    
        return {
            points,
            path,
            minTemp: chartMinTemp,
            maxTemp: chartMaxTemp,
            startTime: new Date(minTime),
            endTime: new Date(maxTime),
        };
    }, [readings]);
    

    return (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4" onClick={onClose}>
            <div className="bg-white rounded-2xl shadow-xl w-full max-w-4xl max-h-[80vh] flex flex-col" onClick={e => e.stopPropagation()}>
                <header className="flex items-center justify-between p-4 border-b border-gray-200">
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800">Temperature History</h2>
                        <p className="text-sm text-gray-500">{sensor.sensor_name}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                         {timeRangeOptions.map(opt => (
                            <button 
                                key={opt.value}
                                onClick={() => setTimeRange(opt.value)}
                                className={`px-3 py-1 text-xs font-medium rounded-full ${timeRange === opt.value ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                            >
                                {opt.label}
                            </button>
                        ))}
                        <button onClick={onClose} className="text-gray-400 hover:text-gray-600 p-1 ml-4">
                            <XIcon className="h-6 w-6" />
                        </button>
                    </div>
                </header>
                <main className="p-6 overflow-y-auto flex-grow relative">
                    {loading && <div className="absolute inset-0 flex items-center justify-center bg-white/70"><p>Loading chart...</p></div>}
                    {error && <div className="text-red-600 text-center"><p>Error: {error}</p></div>}
                    {!loading && !error && chartData && (
                         <div className="h-full">
                            <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none" className="overflow-visible">
                                {/* Y-Axis Labels */}
                                <text x="-1" y="0" dy="0.3em" textAnchor="end" className="text-[4px] fill-gray-500">{chartData.maxTemp}°</text>
                                <text x="-1" y="100" dy="-0.1em" textAnchor="end" className="text-[4px] fill-gray-500">{chartData.minTemp}°</text>
                                
                                {/* Grid Lines */}
                                <line x1="0" y1="0" x2="100" y2="0" className="stroke-gray-200" strokeWidth="0.2" />
                                <line x1="0" y1="25" x2="100" y2="25" className="stroke-gray-200" strokeWidth="0.2" />
                                <line x1="0" y1="50" x2="100" y2="50" className="stroke-gray-200" strokeWidth="0.2" />
                                <line x1="0" y1="75" x2="100" y2="75" className="stroke-gray-200" strokeWidth="0.2" />
                                <line x1="0" y1="100" x2="100" y2="100" className="stroke-gray-200" strokeWidth="0.2" />

                                {/* Data Line */}
                                <path d={chartData.path} fill="none" className="stroke-blue-500" strokeWidth="0.5" strokeLinejoin="round" strokeLinecap="round" />

                                {/* Data Points for hover */}
                                {chartData.points.map((point, i) => (
                                    <g key={i}>
                                         <circle cx={point.x} cy={point.y} r="2" className="fill-blue-500 opacity-0 hover:opacity-100 transition-opacity" />
                                         <title>{`${point.temp.toFixed(1)}° at ${point.time.toLocaleString()}`}</title>
                                    </g>
                                ))}

                                {/* X-Axis Labels */}
                                <text x="0" y="102" dy="0.3em" textAnchor="start" className="text-[4px] fill-gray-500">{chartData.startTime.toLocaleDateString()}</text>
                                <text x="100" y="102" dy="0.3em" textAnchor="end" className="text-[4px] fill-gray-500">{chartData.endTime.toLocaleDateString()}</text>
                            </svg>
                        </div>
                    )}
                    {!loading && !error && !chartData && (
                        <p className="text-center text-gray-500">No data available for this period.</p>
                    )}
                </main>
            </div>
        </div>
    );
};

import React, { useMemo } from 'react';
import { HACCPEvent } from '../types';
import { CubeIcon } from './IconComponents';

interface InventoryDisplayProps {
  events: HACCPEvent[];
}

export const InventoryDisplay: React.FC<InventoryDisplayProps> = ({ events }) => {
  const inventory = useMemo(() => {
    const inventoryMap: { [product: string]: number } = {};

    // Sort events from oldest to newest to calculate inventory chronologically
    [...events].sort((a, b) => {
        const timeA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const timeB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
        return timeA - timeB;
    }).forEach(event => {
        if (!event.product || typeof event.quantity !== 'number') {
            return;
        }

        if (!inventoryMap[event.product]) {
            inventoryMap[event.product] = 0;
        }

        switch (event.eventType) {
            case 'receiving':
                inventoryMap[event.product] += event.quantity;
                break;
            case 'sales':
            case 'disposal':
                inventoryMap[event.product] -= event.quantity;
                break;
            default:
                break;
        }
    });

    // Filter out items with zero or negative quantity and format for display
    return Object.entries(inventoryMap)
        .filter(([, quantity]) => quantity > 0.01) // Use an epsilon for float comparison
        .map(([product, quantity]) => ({
            product,
            quantity,
        }))
        .sort((a, b) => a.product.localeCompare(b.product));
  }, [events]);

  return (
    <div>
        <header className="flex items-center justify-between mb-4">
            <div className="flex items-center">
                <div className="bg-green-100 text-green-600 p-2 rounded-lg mr-4">
                    <CubeIcon className="h-6 w-6" />
                </div>
                <h1 className="text-2xl font-bold text-gray-800">Inventory Summary</h1>
            </div>
        </header>

        {inventory.length === 0 ? (
            <div className="text-center bg-gray-50 p-8 rounded-lg">
                <p className="text-gray-500">No inventory data available. Record a "Receiving" event to get started.</p>
            </div>
        ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                {inventory.map(({ product, quantity }) => (
                    <div key={product} className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                        <h3 className="font-semibold text-gray-800 truncate">{product}</h3>
                        <p className="text-2xl font-bold text-blue-600">{quantity.toFixed(2)}</p>
                        <p className="text-sm text-gray-500">lbs</p>
                    </div>
                ))}
            </div>
        )}
    </div>
  );
};

import React, { useState, useEffect } from 'react';
import { GoogleGenAI } from '@google/genai';
import { EmailContext } from '../types';
import { XIcon } from './IconComponents';

interface EmailDraftModalProps {
  isOpen: boolean;
  onClose: () => void;
  context: EmailContext;
}

const getPromptForContext = (context: EmailContext): string => {
    switch(context.type) {
        case 'order':
            const { order, vendor } = context;
            return `
                Generate a professional and concise purchase order email.
                - To: ${vendor.email || 'Vendor'}
                - From: The purchasing manager.
                - Subject: Purchase Order - ${order.species}
                - Body: Clearly state that we would like to place an order for ${order.quantity} lbs of ${order.species}.
                - Mention the desired delivery date is on or before ${order.expectedDeliveryDate}.
                - Ask them to confirm receipt of this PO and the expected delivery date.
                - Keep it friendly and professional. Do not use placeholders like [Your Name].
            `;
        case 'receiving':
            const { event } = context;
            const product = [event.product, event.productForm].filter(Boolean).join(' ');
            return `
                Generate a professional and concise internal memo for the warehouse team about an upcoming delivery.
                - To: Warehouse Team
                - From: Management
                - Subject: Incoming Delivery Notification: ${product} from ${event.supplier}
                - Body: Inform the team to expect a delivery of ${event.quantity} lbs of ${product} from ${event.supplier} on ${event.date}.
                - Specify that the product should be stored at location: ${event.location}.
                - Remind them to perform standard quality and temperature checks upon arrival (target temp: < 40°F if fresh, ~32°F if previously frozen).
                - Keep it clear and direct.
            `;
    }
};

export const EmailDraftModal: React.FC<EmailDraftModalProps> = ({ isOpen, onClose, context }) => {
    const [draft, setDraft] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');

    useEffect(() => {
        if (isOpen && context) {
            const generateDraft = async () => {
                setIsLoading(true);
                setError('');
                setDraft('');
                try {
                    const ai = new GoogleGenAI({ apiKey: process.env.API_KEY as string });
                    const prompt = getPromptForContext(context);
                    const response = await ai.models.generateContent({
                        model: 'gemini-2.5-flash',
                        contents: prompt,
                    });
                    setDraft(response.text);
                } catch (err: any) {
                    console.error("Error generating email draft:", err);
                    if (err.message?.includes('does not have permission') || err.message?.includes('not found')) {
                        setError("API Key Error. Please close this window and re-select your API key from the main screen.");
                    } else {
                        setError("Sorry, I couldn't generate the email draft. Please try again.");
                    }
                } finally {
                    setIsLoading(false);
                }
            };
            generateDraft();
        }
    }, [isOpen, context]);

    if (!isOpen) return null;

    const handleCopyToClipboard = () => {
        navigator.clipboard.writeText(draft);
        alert('Email draft copied to clipboard!');
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4" onClick={onClose}>
            <div className="bg-white rounded-2xl shadow-xl w-full max-w-2xl max-h-[80vh] flex flex-col" onClick={e => e.stopPropagation()}>
                <header className="flex items-center justify-between p-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-800">AI-Generated Email Draft</h2>
                    <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
                        <XIcon className="h-6 w-6" />
                    </button>
                </header>
                <div className="p-6 overflow-y-auto">
                    {isLoading && <p className="text-center text-gray-500">Generating draft...</p>}
                    {error && <p className="text-center text-red-500">{error}</p>}
                    {!isLoading && !error && (
                        <textarea
                            value={draft}
                            onChange={(e) => setDraft(e.target.value)}
                            className="w-full h-96 p-3 border border-gray-300 rounded-md shadow-sm font-mono text-sm"
                        />
                    )}
                </div>
                <footer className="p-4 border-t border-gray-200 flex justify-end">
                    <button
                        onClick={handleCopyToClipboard}
                        disabled={isLoading || !!error}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-blue-700 disabled:bg-gray-400"
                    >
                        Copy to Clipboard
                    </button>
                </footer>
            </div>
        </div>
    );
};

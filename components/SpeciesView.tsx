import React, { useState, useEffect } from 'react';
import { db } from '../firebase/config';
import { collection, onSnapshot, query, orderBy, addDoc, updateDoc, doc, QuerySnapshot, DocumentData } from 'firebase/firestore';
import { Species } from '../types';
import { FishIcon, PlusIcon, PhotoIcon } from './IconComponents';
import { uploadImage } from '../utils/imageUtils';
import { docToPlainObject } from '../utils/firestoreUtils';

export const SpeciesView: React.FC = () => {
    const [speciesList, setSpeciesList] = useState<Species[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isFormOpen, setIsFormOpen] = useState(false);
    
    // Form state
    const [editingId, setEditingId] = useState<string | null>(null);
    const [name, setName] = useState('');
    const [productForms, setProductForms] = useState('');
    const [qualityControlNotes, setQualityControlNotes] = useState('');
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);

    useEffect(() => {
        try {
            const q = query(collection(db, "species"), orderBy("name"));
            const unsubscribe = onSnapshot(q, (querySnapshot: QuerySnapshot<DocumentData>) => {
                const list: Species[] = querySnapshot.docs.map(doc => docToPlainObject<Species>(doc));
                setSpeciesList(list);
                setLoading(false);
            }, (err) => {
                console.error(err);
                setError("Failed to fetch species data.");
                setLoading(false);
            });
            return () => unsubscribe();
        } catch(e) {
            console.error(e);
            setError("Failed to initialize Firebase for species.");
            setLoading(false);
        }
    }, []);

    const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setImageFile(file);
            const previewUrl = URL.createObjectURL(file);
            setImagePreview(previewUrl);
        }
    };

    const resetForm = () => {
        setEditingId(null);
        setName('');
        setProductForms('');
        setQualityControlNotes('');
        setImageFile(null);
        setImagePreview(null);
        setIsFormOpen(false);
    };

    const handleEdit = (species: Species) => {
        setEditingId(species.id);
        setName(species.name);
        setProductForms(species.productForms.join('\n'));
        setQualityControlNotes(species.qualityControlNotes);
        setImagePreview(species.imageUrl || null);
        setImageFile(null);
        setIsFormOpen(true);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!name || !qualityControlNotes) {
            alert("Species name and quality control notes are required.");
            return;
        }
        
        const formsArray = productForms.split('\n').map(s => s.trim()).filter(Boolean);
        const speciesData: Partial<Species> = { name, productForms: formsArray, qualityControlNotes };

        if (imageFile) {
            try {
                const imageUrl = await uploadImage(imageFile, `species/${Date.now()}_${imageFile.name}`);
                speciesData.imageUrl = imageUrl;
            } catch (error) {
                alert("Failed to upload image.");
                return;
            }
        } else if (editingId && imagePreview) {
            speciesData.imageUrl = imagePreview;
        }


        try {
            if (editingId) {
                const speciesRef = doc(db, "species", editingId);
                await updateDoc(speciesRef, speciesData);
            } else {
                await addDoc(collection(db, "species"), speciesData);
            }
            resetForm();
        } catch (error) {
            console.error("Error saving species: ", error);
            alert("Failed to save species data.");
        }
    };

    return (
        <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 w-full space-y-6">
            <header className="flex items-center justify-between pb-4 border-b border-gray-200">
                <div className="flex items-center">
                    <div className="bg-green-100 text-green-600 p-2 rounded-lg mr-4">
                        <FishIcon className="h-6 w-6" />
                    </div>
                    <h1 className="text-2xl font-bold text-gray-800">Species Information</h1>
                </div>
                <button onClick={() => { resetForm(); setIsFormOpen(true); }} className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-blue-700">
                    <PlusIcon className="h-5 w-5 mr-2" />
                    Add Species
                </button>
            </header>

            {isFormOpen && (
                 <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                    <h2 className="text-xl font-semibold mb-4 text-gray-700">{editingId ? 'Edit Species' : 'Add New Species'}</h2>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <label htmlFor="name" className="block text-sm font-medium text-gray-700">Species Name</label>
                            <input type="text" id="name" value={name} onChange={e => setName(e.target.value)} required className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                        </div>
                        <div>
                            <label htmlFor="productForms" className="block text-sm font-medium text-gray-700">Product Forms (one per line)</label>
                            <textarea id="productForms" value={productForms} onChange={e => setProductForms(e.target.value)} rows={4} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"></textarea>
                        </div>
                        <div>
                            <label htmlFor="qualityControlNotes" className="block text-sm font-medium text-gray-700">Quality Control Violations & Notes</label>
                            <textarea id="qualityControlNotes" value={qualityControlNotes} onChange={e => setQualityControlNotes(e.target.value)} required rows={6} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"></textarea>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Species Image</label>
                            <div className="mt-2 flex items-center space-x-4">
                                    <div className="shrink-0">
                                    {imagePreview ? (
                                        <img className="h-16 w-16 object-cover rounded-md" src={imagePreview} alt="Species" />
                                    ) : (
                                        <div className="h-16 w-16 bg-gray-200 rounded-md flex items-center justify-center">
                                            <PhotoIcon className="h-8 w-8 text-gray-400" />
                                        </div>
                                    )}
                                </div>
                                <input type="file" onChange={handleImageSelect} accept="image/*" className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"/>
                            </div>
                        </div>
                        <div className="flex justify-end space-x-3">
                            <button type="button" onClick={resetForm} className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">Cancel</button>
                            <button type="submit" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">{editingId ? 'Update Species' : 'Save Species'}</button>
                        </div>
                    </form>
                </div>
            )}

            {loading && <p>Loading species...</p>}
            {error && <p className="text-red-500">{error}</p>}
            {!loading && !error && (
                <div className="space-y-4">
                    {speciesList.length === 0 ? <p className="text-center text-gray-500 py-4">No species data added yet.</p> :
                        speciesList.map(s => (
                            <div key={s.id} className="p-4 border rounded-lg bg-white hover:bg-gray-50 flex items-start space-x-4">
                                {s.imageUrl && (
                                    <img src={s.imageUrl} alt={s.name} className="h-24 w-24 rounded-lg object-cover flex-shrink-0" />
                                )}
                                <div className="flex-grow">
                                    <div className="flex justify-between items-start">
                                        <h3 className="font-semibold text-lg text-gray-800">{s.name}</h3>
                                        <button onClick={() => handleEdit(s)} className="text-blue-600 hover:text-blue-800 text-sm">Edit</button>
                                    </div>
                                    <div className="mt-2 text-sm text-gray-700">
                                        <h4 className="font-semibold">Product Forms:</h4>
                                        <p className="text-gray-600">{s.productForms.join(', ') || 'N/A'}</p>
                                    </div>
                                    <div className="mt-2 text-sm text-gray-700">
                                        <h4 className="font-semibold">Quality Control Notes:</h4>
                                        <p className="text-gray-600 whitespace-pre-wrap">{s.qualityControlNotes}</p>
                                    </div>
                                </div>
                            </div>
                        ))
                    }
                </div>
            )}
        </div>
    );
};
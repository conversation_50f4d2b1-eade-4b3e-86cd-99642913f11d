import React, { useState, useCallback, useMemo, useEffect } from 'react';
import * as xlsx from 'xlsx';
import { GoogleGenAI, Type } from '@google/genai';
import { HACCPEvent, Species, Vendor, Location, EventType } from '../types';
import { XIcon, DocumentArrowUpIcon, TrashIcon } from './IconComponents';

interface ImportModalProps {
    species: Species[];
    vendors: Vendor[];
    locations: Location[];
    onClose: () => void;
    onSave: (events: Partial<HACCPEvent>[]) => void;
}

type ModalStep = 'upload' | 'loading' | 'confirm' | 'error';

type TempHACCPEvent = Partial<HACCPEvent> & { tempId: number };

export const ImportModal: React.FC<ImportModalProps> = ({ species, vendors, locations, onClose, onSave }) => {
    const [step, setStep] = useState<ModalStep>('upload');
    const [originalEvents, setOriginalEvents] = useState<TempHACCPEvent[]>([]);
    const [extractedEvents, setExtractedEvents] = useState<TempHACCPEvent[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [isDragging, setIsDragging] = useState(false);
    const [overrideEventType, setOverrideEventType] = useState<EventType | 'auto'>('auto');

    const handleFile = useCallback(async (file: File) => {
        setStep('loading');
        setError(null);
        setOverrideEventType('auto');
        try {
            const data = await file.arrayBuffer();
            const workbook = xlsx.read(data);
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const json: any[] = xlsx.utils.sheet_to_json(worksheet);

            if (json.length === 0) {
                setError("The uploaded file is empty or in an unsupported format.");
                setStep('error');
                return;
            }

            const headers = Object.keys(json[0]).join(', ');
            const fullDataString = json.map(row => JSON.stringify(row)).join('\n');
            
            const ai = new GoogleGenAI({ apiKey: process.env.API_KEY as string });
            
            const response = await ai.models.generateContent({
                model: 'gemini-2.5-flash',
                contents: { parts: [{ text: `
                    Analyze the following data extracted from a user's spreadsheet and convert it into a JSON array of HACCP events.
                    
                    File Headers: ${headers}
                    Spreadsheet Data:
                    ${fullDataString}

                    Existing database values for matching:
                    - Species: ${species.map(s => s.name).join(', ')}
                    - Vendors: ${vendors.map(v => v.name).join(', ')}
                    - Locations: ${locations.map(l => l.name).join(', ')}

                    Your task is to map the spreadsheet columns to the fields in the provided JSON schema.
                    - Infer the 'eventType' from the data (e.g., if there's a supplier, it's likely 'receiving'). Default to 'inventory' if unsure.
                    - If a date is present, use it in YYYY-MM-DD format. Standardize date formats if possible (e.g., MM/DD/YY -> YYYY-MM-DD). If no year is present assume the current year.
                    - Ensure 'quantity' and 'temperature' are numbers.
                    - Be precise. If a value for a field isn't in the data, omit that key from the object.
                    - Your output MUST be ONLY the JSON array.
                `}] },
                config: {
                    responseMimeType: 'application/json',
                    responseSchema: {
                        type: Type.ARRAY,
                        items: {
                            type: Type.OBJECT,
                            properties: {
                                eventType: { type: Type.STRING, enum: ['receiving', 'inventory', 'disposal', 'sales', 'sanitation', 'relocation', 're-sealing'] },
                                product: { type: Type.STRING, description: 'The name of the seafood species.' },
                                productForm: { type: Type.STRING, description: 'The form of the product, e.g., Fillet, Whole.' },
                                quantity: { type: Type.NUMBER, description: 'The quantity of the product.' },
                                unit: { type: Type.STRING, description: 'The unit of measurement, e.g., lbs.' },
                                supplier: { type: Type.STRING, description: 'The name of the vendor or supplier.' },
                                location: { type: Type.STRING, description: 'The storage location.' },
                                date: { type: Type.STRING, description: 'The date of the event in YYYY-MM-DD format.' },
                                batchNumber: { type: Type.STRING, description: 'The batch or lot number.' },
                                temperature: { type: Type.NUMBER, description: 'The temperature in Fahrenheit.' },
                                notes: { type: Type.STRING, description: 'Any other relevant notes from the document.' },
                            },
                        },
                    },
                },
            });
            
            const parsedEvents = JSON.parse(response.text).map((event: Partial<HACCPEvent>, index: number) => ({
                ...event,
                tempId: index,
            }));
            setOriginalEvents(parsedEvents);
            setExtractedEvents(parsedEvents);
            setStep('confirm');

        } catch (err: any) {
            console.error("Error processing file:", err);
            if (err.message?.includes('does not have permission') || err.message?.includes('not found')) {
                setError("API Key Error. Please close and re-select your API key from the main screen.");
            } else {
                setError("AI analysis failed. Please ensure the file is a standard CSV or Excel format and try again.");
            }
            setStep('error');
        }
    }, [species, vendors, locations]);
    
    useEffect(() => {
        if (step !== 'confirm' || originalEvents.length === 0) return;

        if (overrideEventType === 'auto') {
            setExtractedEvents(currentEvents =>
                currentEvents.map(event => {
                    const originalEvent = originalEvents.find(o => o.tempId === event.tempId);
                    return {
                        ...event,
                        eventType: originalEvent ? originalEvent.eventType : 'inventory',
                    };
                })
            );
        } else {
            setExtractedEvents(currentEvents =>
                currentEvents.map(event => ({
                    ...event,
                    eventType: overrideEventType,
                }))
            );
        }
    }, [overrideEventType, originalEvents, step]);

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            handleFile(e.target.files[0]);
        }
    };
    
    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFile(e.dataTransfer.files[0]);
        }
    };

    const handleDataChange = (id: number, field: keyof HACCPEvent, value: string | number) => {
        setExtractedEvents(currentEvents =>
            currentEvents.map(event =>
                event.tempId === id ? { ...event, [field]: value } : event
            )
        );
    };

    const removeEvent = (id: number) => {
        setExtractedEvents(prev => prev.filter(event => event.tempId !== id));
        setOriginalEvents(prev => prev.filter(event => event.tempId !== id));
    };

    const { newSpecies, newVendors, newLocations } = useMemo(() => {
        const existingSpecies = new Set(species.map(s => s.name.toLowerCase()));
        const existingVendors = new Set(vendors.map(v => v.name.toLowerCase()));
        const existingLocations = new Set(locations.map(l => l.name.toLowerCase()));
        const newSpecies = new Set<string>();
        const newVendors = new Set<string>();
        const newLocations = new Set<string>();
        extractedEvents.forEach(event => {
            if (event.product && !existingSpecies.has(event.product.toLowerCase())) newSpecies.add(event.product);
            if (event.supplier && !existingVendors.has(event.supplier.toLowerCase())) newVendors.add(event.supplier);
            if (event.location && !existingLocations.has(event.location.toLowerCase())) newLocations.add(event.location);
        });
        return { newSpecies: [...newSpecies], newVendors: [...newVendors], newLocations: [...newLocations] };
    }, [extractedEvents, species, vendors, locations]);

    const renderContent = () => {
        switch (step) {
            case 'upload':
                return (
                    <div className="p-6 flex flex-col items-center justify-center text-center h-full">
                        <div
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDrop}
                            className={`w-full p-10 border-2 border-dashed rounded-lg flex flex-col items-center justify-center space-y-4 transition-colors ${isDragging ? 'border-blue-500 bg-blue-900/30' : 'border-gray-600'}`}
                        >
                            <DocumentArrowUpIcon className="h-16 w-16 text-gray-400" />
                            <p className="text-lg font-semibold">Drag & drop your CSV or Excel file here</p>
                            <p className="text-gray-400">or</p>
                            <label htmlFor="file-upload" className="cursor-pointer bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                Browse Files
                            </label>
                            <input id="file-upload" type="file" className="hidden" onChange={handleFileSelect} accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
                        </div>
                        <p className="mt-4 text-xs text-gray-500">Your data is processed locally in your browser.</p>
                    </div>
                );
            case 'loading':
                return (
                    <div className="flex flex-col items-center justify-center h-full text-white">
                        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white"></div>
                        <p className="mt-4 text-lg">Parsing file & analyzing data...</p>
                    </div>
                );
            case 'error':
                 return (
                    <div className="flex flex-col items-center justify-center h-full text-white text-center p-6">
                        <p className="text-xl text-red-500 font-semibold">An Error Occurred</p>
                        <p className="mt-2 text-gray-300">{error}</p>
                        <button onClick={() => setStep('upload')} className="mt-6 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            Try Again
                        </button>
                    </div>
                );
            case 'confirm':
                const hasNewItems = newSpecies.length > 0 || newVendors.length > 0 || newLocations.length > 0;
                return (
                    <div className="p-6 flex-grow overflow-y-auto">
                        <h3 className="text-xl font-bold mb-4 text-white">Confirm Imported Data</h3>
                         <div className="bg-gray-800 p-3 rounded-lg mb-4 flex items-center space-x-4">
                            <label htmlFor="eventTypeOverride" className="text-sm font-medium text-gray-300 flex-shrink-0">Set all events to type:</label>
                            <select
                                id="eventTypeOverride"
                                value={overrideEventType}
                                onChange={e => setOverrideEventType(e.target.value as EventType | 'auto')}
                                className="bg-gray-700 p-2 rounded w-full text-sm"
                            >
                                <option value="auto">Auto-Detect (from file)</option>
                                <option value="receiving">Receiving</option>
                                <option value="sales">Sales</option>
                                <option value="disposal">Disposal</option>
                                <option value="relocation">Relocation</option>
                                <option value="re-sealing">Re-sealing</option>
                                <option value="inventory">Inventory Check</option>
                                <option value="sanitation">Sanitation</option>
                            </select>
                        </div>
                        {hasNewItems && (
                             <div className="bg-blue-900/50 border border-blue-700 p-3 rounded-lg mb-4 text-sm">
                                <p className="font-semibold text-blue-300">New items detected!</p>
                                <p className="text-blue-400">These will be added to your database upon saving:</p>
                                <ul className="list-disc list-inside text-xs mt-1">
                                    {newSpecies.length > 0 && <li><strong>Species:</strong> {newSpecies.join(', ')}</li>}
                                    {newVendors.length > 0 && <li><strong>Vendors:</strong> {newVendors.join(', ')}</li>}
                                    {newLocations.length > 0 && <li><strong>Locations:</strong> {newLocations.join(', ')}</li>}
                                </ul>
                            </div>
                        )}
                        <div className="space-y-4">
                           {extractedEvents.map(event => {
                                const isNewProduct = event.product && newSpecies.includes(event.product);
                                const isNewSupplier = event.supplier && newVendors.includes(event.supplier);
                                const isNewLocation = event.location && newLocations.includes(event.location);

                                return (
                                <div key={event.tempId} className="bg-gray-800 p-4 rounded-lg space-y-2 relative">
                                    <button onClick={() => removeEvent(event.tempId)} className="absolute top-2 right-2 text-gray-400 hover:text-red-500"><TrashIcon className="h-5 w-5"/></button>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                                        <div className="relative">
                                            <label className="text-xs text-gray-400">Event</label>
                                            <input value={event.eventType || ''} onChange={e => handleDataChange(event.tempId, 'eventType', e.target.value)} disabled={overrideEventType !== 'auto'} className="bg-gray-700 p-2 rounded w-full disabled:bg-gray-600 disabled:text-gray-400"/>
                                        </div>
                                        <div className="relative">
                                            <label className="text-xs text-gray-400">Product</label>
                                            <input value={event.product || ''} onChange={e => handleDataChange(event.tempId, 'product', e.target.value)} className={`bg-gray-700 p-2 rounded w-full ${isNewProduct ? 'border border-green-500' : 'border-transparent'} border-2`}/>
                                            {isNewProduct && <span className="absolute top-0 right-0 mt-1 mr-1 px-2 py-0.5 bg-green-500 text-white text-xs rounded-full">New</span>}
                                        </div>
                                        <div className="relative">
                                            <label className="text-xs text-gray-400">Quantity</label>
                                            <input type="number" value={event.quantity || ''} onChange={e => handleDataChange(event.tempId, 'quantity', parseFloat(e.target.value))} className="bg-gray-700 p-2 rounded w-full"/>
                                        </div>
                                        <div className="relative">
                                            <label className="text-xs text-gray-400">Supplier</label>
                                            <input value={event.supplier || ''} onChange={e => handleDataChange(event.tempId, 'supplier', e.target.value)} className={`bg-gray-700 p-2 rounded w-full ${isNewSupplier ? 'border border-green-500' : 'border-transparent'} border-2`}/>
                                            {isNewSupplier && <span className="absolute top-0 right-0 mt-1 mr-1 px-2 py-0.5 bg-green-500 text-white text-xs rounded-full">New</span>}
                                        </div>
                                        <div className="relative">
                                            <label className="text-xs text-gray-400">Location</label>
                                            <input value={event.location || ''} onChange={e => handleDataChange(event.tempId, 'location', e.target.value)} className={`bg-gray-700 p-2 rounded w-full ${isNewLocation ? 'border border-green-500' : 'border-transparent'} border-2`}/>
                                            {isNewLocation && <span className="absolute top-0 right-0 mt-1 mr-1 px-2 py-0.5 bg-green-500 text-white text-xs rounded-full">New</span>}
                                        </div>
                                        <div className="relative">
                                            <label className="text-xs text-gray-400">Date</label>
                                            <input type="date" value={event.date || ''} onChange={e => handleDataChange(event.tempId, 'date', e.target.value)} className="bg-gray-700 p-2 rounded w-full"/>
                                        </div>
                                        <div className="relative sm:col-span-2 md:col-span-3">
                                            <label className="text-xs text-gray-400">Notes</label>
                                            <input value={event.notes || ''} onChange={e => handleDataChange(event.tempId, 'notes', e.target.value)} className="bg-gray-700 p-2 rounded w-full"/>
                                        </div>
                                    </div>
                                </div>
                                );
                           })}
                        </div>
                    </div>
                );
            default: return null;
        }
    };
    
    return (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4">
            <div className="bg-gray-900 text-white rounded-2xl shadow-2xl w-full max-w-4xl flex flex-col h-[90vh] border border-gray-700">
                <header className="flex items-center justify-between p-4 border-b border-gray-700 flex-shrink-0">
                    <h2 className="text-xl font-bold">Import from File</h2>
                    <button onClick={onClose} className="text-gray-400 hover:text-white"><XIcon className="h-6 w-6" /></button>
                </header>
                <main className="flex-grow overflow-hidden flex flex-col">
                    {renderContent()}
                </main>
                 {step === 'confirm' && (
                    <footer className="p-4 border-t border-gray-700 flex justify-end items-center space-x-4 flex-shrink-0">
                        <button onClick={onClose} className="bg-gray-600 text-white px-6 py-2 rounded-lg font-semibold">Cancel</button>
                        <button onClick={() => onSave(extractedEvents)} className="bg-green-600 text-white px-6 py-2 rounded-lg font-semibold" disabled={extractedEvents.length === 0}>Save {extractedEvents.length} Items</button>
                    </footer>
                )}
            </div>
        </div>
    );
};
import React from 'react';

type OldViews = 'vendors' | 'species' | 'locations' | 'orders' | 'temperature';

interface SettingsViewProps {
    onNavigate: (view: OldViews) => void;
}

export const SettingsView: React.FC<SettingsViewProps> = ({ onNavigate }) => {
  return (
    <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 w-full mx-auto">
      <h1 className="text-2xl font-bold text-gray-800 mb-6">Settings</h1>
      <p className="text-gray-600 mb-8">This is a placeholder for the Settings view. You can access the other management pages from here.</p>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <button
            onClick={() => onNavigate('vendors')}
            className="p-4 bg-gray-100 hover:bg-gray-200 rounded-lg text-gray-700 font-semibold transition-colors"
        >
            Manage Vendors
        </button>
        <button
            onClick={() => onNavigate('species')}
            className="p-4 bg-gray-100 hover:bg-gray-200 rounded-lg text-gray-700 font-semibold transition-colors"
        >
            Manage Species
        </button>
        <button
            onClick={() => onNavigate('locations')}
            className="p-4 bg-gray-100 hover:bg-gray-200 rounded-lg text-gray-700 font-semibold transition-colors"
        >
            Manage Locations
        </button>
        <button
            onClick={() => onNavigate('orders')}
            className="p-4 bg-gray-100 hover:bg-gray-200 rounded-lg text-gray-700 font-semibold transition-colors"
        >
            Manage Orders
        </button>
        <button
            onClick={() => onNavigate('temperature')}
            className="p-4 bg-gray-100 hover:bg-gray-200 rounded-lg text-gray-700 font-semibold transition-colors"
        >
            Temperature Logs
        </button>
      </div>
    </div>
  );
};
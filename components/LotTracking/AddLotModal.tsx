import React, { useState } from 'react';
import { addLot } from '../../firebase/lotService';
import { Lot, LotStatus } from '../../types';

interface AddLotModalProps {
  onClose: () => void;
}

export const AddLotModal: React.FC<AddLotModalProps> = ({ onClose }) => {
  const [lotId, setLotId] = useState('');
  const [species, setSpecies] = useState('');
  const [origin, setOrigin] = useState('');
  const [initialWeight, setInitialWeight] = useState('');
  const [harvestDate, setHarvestDate] = useState(new Date().toISOString().split('T')[0]);
  const [status, setStatus] = useState<LotStatus>('Available');
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!lotId || !species || !origin || !initialWeight || !harvestDate) {
      setError('Please fill out all fields.');
      return;
    }

    const newLot: Omit<Lot, 'id' | 'createdAt' | 'updatedAt'> = {
      lotId,
      species,
      origin,
      initialWeight: parseFloat(initialWeight),
      harvestDate,
      status,
    };

    try {
      await addLot(newLot);
      onClose();
    } catch (err) {
      setError('Failed to create lot. Please try again.');
      console.error(err);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-[#152329] rounded-xl shadow-lg p-6 sm:p-8 max-w-lg w-full">
        <div className="flex justify-between items-center mb-6 pb-4 border-b border-gray-200/80 dark:border-gray-700/50">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">Track New Lot</h2>
          <button onClick={onClose} className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700/50">
            <span className="material-symbols-outlined text-gray-600 dark:text-gray-400">close</span>
          </button>
        </div>
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Lot ID</label>
              <input type="text" value={lotId} onChange={e => setLotId(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-transparent" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Species</label>
              <input type="text" value={species} onChange={e => setSpecies(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-transparent" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Origin</label>
              <input type="text" value={origin} onChange={e => setOrigin(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-transparent" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Initial Weight (kg)</label>
              <input type="number" value={initialWeight} onChange={e => setInitialWeight(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-transparent" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Harvest Date</label>
              <input type="date" value={harvestDate} onChange={e => setHarvestDate(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-transparent" />
            </div>
             <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                <select value={status} onChange={e => setStatus(e.target.value as LotStatus)} className="mt-1 w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-transparent">
                    <option value="Available">Available</option>
                    <option value="In-Transit">In-Transit</option>
                    <option value="Processed">Processed</option>
                    <option value="Warning">Warning</option>
                </select>
            </div>
          </div>
          {error && <p className="text-red-500 text-sm mb-4">{error}</p>}
          <div className="flex justify-end gap-4">
            <button type="button" onClick={onClose} className="bg-gray-200/50 dark:bg-gray-700/30 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg hover:bg-gray-300/50 dark:hover:bg-gray-600/30">Cancel</button>
            <button type="submit" className="bg-primary text-white px-4 py-2 rounded-lg shadow hover:bg-primary/90">Save Lot</button>
          </div>
        </form>
      </div>
    </div>
  );
};
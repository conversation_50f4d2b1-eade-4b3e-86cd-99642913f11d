import React from 'react';
import { LotMovement } from '../../types';

interface MovementHistoryProps {
  movements: LotMovement[];
  onAddMovement: () => void;
}

const getIconColor = (type: string) => {
    switch (type) {
        case 'Arrival': return 'bg-green-500';
        case 'Departure': return 'bg-red-500';
        default: return 'bg-gray-400';
    }
}

export const MovementHistory: React.FC<MovementHistoryProps> = ({ movements, onAddMovement }) => {
  return (
    <div className="rounded-xl border border-gray-200/80 dark:border-gray-700/50 bg-white dark:bg-[#152329] p-5 flex-1 flex flex-col">
      <h3 className="font-bold text-gray-900 dark:text-white mb-4">Movement History</h3>
      <div className="flex-1 overflow-y-auto pr-2 -mr-2">
        <ul className="space-y-6 border-l-2 border-gray-200 dark:border-gray-700/50 ml-3">
          {movements.map((movement) => (
            <li key={movement.id} className="relative pl-8">
              <div className={`absolute -left-[11px] top-1.5 size-5 ${getIconColor(movement.type)} rounded-full border-4 border-white dark:border-[#152329]`}></div>
              <p className="text-sm font-semibold text-gray-800 dark:text-gray-200">{movement.description}</p>
              <p className="text-xs text-gray-500 dark:text-gray-400">{new Date(movement.timestamp.seconds * 1000).toLocaleString()}</p>
            </li>
          ))}
        </ul>
      </div>
      <button
        onClick={onAddMovement}
        className="mt-4 flex w-full cursor-pointer items-center justify-center gap-2 overflow-hidden rounded-lg h-10 px-4 bg-gray-200/50 dark:bg-gray-700/30 text-gray-700 dark:text-gray-300 text-sm font-medium leading-normal hover:bg-gray-300/50 dark:hover:bg-gray-600/30">
        <span className="material-symbols-outlined text-base">add</span>
        <span className="truncate">Add New Movement</span>
      </button>
    </div>
  );
};
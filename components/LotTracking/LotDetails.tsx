import React, { useState, useEffect } from 'react';
import { Lot, LotMovement } from '../../types';
import { onLotMovementsUpdate } from '../../firebase/lotService';
import { MovementHistory } from './MovementHistory';
import { AddMovementModal } from './AddMovementModal';

interface LotDetailsProps {
  lot: Lot;
}

export const LotDetails: React.FC<LotDetailsProps> = ({ lot }) => {
  const [movements, setMovements] = useState<LotMovement[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (!lot.id) return;
    const unsubscribe = onLotMovementsUpdate(lot.id, setMovements);
    return () => unsubscribe();
  }, [lot.id]);

  const currentStatus = movements.length > 0 ? movements[0] : null;

  return (
    <>
      <div className="rounded-xl border border-gray-200/80 dark:border-gray-700/50 bg-white dark:bg-[#152329] p-5">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-bold text-gray-900 dark:text-white">Details for Lot #{lot.lotId}</h2>
          <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700/50">
            <span className="material-symbols-outlined text-gray-600 dark:text-gray-400 text-xl">ios_share</span>
          </button>
        </div>
        <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500 dark:text-gray-400">Species</p>
            <p className="font-medium text-gray-800 dark:text-gray-200">{lot.species}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Origin</p>
            <p className="font-medium text-gray-800 dark:text-gray-200">{lot.origin}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Initial Weight</p>
            <p className="font-medium text-gray-800 dark:text-gray-200">{lot.initialWeight} kg</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Harvest Date</p>
            <p className="font-medium text-gray-800 dark:text-gray-200">{lot.harvestDate}</p>
          </div>
        </div>
      </div>

      {currentStatus && (
        <div className="rounded-xl border border-gray-200/80 dark:border-gray-700/50 bg-white dark:bg-[#152329] p-5">
            <h3 className="font-bold text-gray-900 dark:text-white mb-3">Current Status</h3>
            <div className="flex items-start gap-4">
                <div className="bg-primary/10 rounded-full p-3 flex items-center justify-center">
                    <span className="material-symbols-outlined text-primary text-2xl">warehouse</span>
                </div>
                <div>
                    <p className="font-semibold text-gray-800 dark:text-gray-200">{currentStatus.location}</p>
                    <p className="text-gray-500 dark:text-gray-400 text-sm">Last Update: {new Date(currentStatus.timestamp.seconds * 1000).toLocaleString()}</p>
                    <span className={`mt-2 inline-block px-2.5 py-1 text-xs font-medium rounded-full ${
                      lot.status === 'Available' ? 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-300' :
                      lot.status === 'In-Transit' ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300' :
                      lot.status === 'Processed' ? 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-300' :
                      'bg-orange-100 dark:bg-orange-900/50 text-orange-800 dark:text-orange-300'
                    }`}>{lot.status}</span>
                </div>
            </div>
        </div>
      )}

      <MovementHistory movements={movements} onAddMovement={() => setIsModalOpen(true)} />

      {isModalOpen && (
        <AddMovementModal lotId={lot.id} onClose={() => setIsModalOpen(false)} />
      )}
    </>
  );
};
import React, { useMemo } from 'react';
import { Lot, LotMovement } from '../../types';

interface LotListProps {
  lots: Lot[];
  movements: LotMovement[];
  selectedLot: Lot | null;
  onSelectLot: (lot: Lot) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export const LotList: React.FC<LotListProps> = ({ lots, movements, selectedLot, onSelectLot, searchQuery, onSearchChange }) => {
  const latestMovements = useMemo(() => {
    const movementMap = new Map<string, LotMovement>();
    movements.forEach(movement => {
        if (!movementMap.has(movement.lotId)) {
            movementMap.set(movement.lotId, movement);
        }
    });
    return movementMap;
  }, [movements]);

  return (
    <>
      <div className="flex flex-col gap-3">
        {/* SearchBar */}
        <div className="w-full">
          <label className="flex flex-col w-full">
            <div className="flex w-full items-stretch rounded-lg bg-white dark:bg-[#152329] border border-gray-200/80 dark:border-gray-700/50 focus-within:ring-2 focus-within:ring-primary/50">
              <div className="text-gray-500 dark:text-gray-400 flex items-center justify-center pl-4">
                <span className="material-symbols-outlined">search</span>
              </div>
              <input
                className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden text-gray-900 dark:text-white focus:outline-0 focus:ring-0 border-none bg-transparent h-12 placeholder:text-gray-500 dark:placeholder:text-gray-400 px-4 pl-2 text-base font-normal leading-normal"
                placeholder="Search by Lot ID, Species, Origin..."
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
              />
            </div>
          </label>
        </div>
        {/* Chips */}
        <div className="flex gap-3 overflow-x-auto pb-2">
          <button className="flex h-9 shrink-0 items-center justify-center gap-x-2 rounded-lg bg-gray-200/50 dark:bg-gray-700/30 px-3 hover:bg-gray-300/50 dark:hover:bg-gray-600/30">
            <p className="text-gray-700 dark:text-gray-300 text-sm font-medium leading-normal">Status: All</p>
            <span className="material-symbols-outlined text-gray-500 dark:text-gray-400 text-lg">expand_more</span>
          </button>
          <button className="flex h-9 shrink-0 items-center justify-center gap-x-2 rounded-lg bg-gray-200/50 dark:bg-gray-700/30 px-3 hover:bg-gray-300/50 dark:hover:bg-gray-600/30">
            <p className="text-gray-700 dark:text-gray-300 text-sm font-medium leading-normal">Location: All</p>
            <span className="material-symbols-outlined text-gray-500 dark:text-gray-400 text-lg">expand_more</span>
          </button>
          <button className="flex h-9 shrink-0 items-center justify-center gap-x-2 rounded-lg bg-gray-200/50 dark:bg-gray-700/30 px-3 hover:bg-gray-300/50 dark:hover:bg-gray-600/30">
            <p className="text-gray-700 dark:text-gray-300 text-sm font-medium leading-normal">Species: All</p>
            <span className="material-symbols-outlined text-gray-500 dark:text-gray-400 text-lg">expand_more</span>
          </button>
          <button className="flex h-9 shrink-0 items-center justify-center gap-x-2 rounded-lg bg-gray-200/50 dark:bg-gray-700/30 px-3 hover:bg-gray-300/50 dark:hover:bg-gray-600/30">
            <p className="text-gray-700 dark:text-gray-300 text-sm font-medium leading-normal">Date Range</p>
            <span className="material-symbols-outlined text-gray-500 dark:text-gray-400 text-lg">expand_more</span>
          </button>
        </div>
      </div>
      {/* Table */}
      <div className="flex-1 overflow-hidden rounded-xl border border-gray-200/80 dark:border-gray-700/50 bg-white dark:bg-[#152329]">
        <div className="overflow-x-auto h-full">
          <table className="w-full text-left">
            <thead className="bg-gray-50 dark:bg-gray-800/20">
              <tr>
                <th className="p-4 text-sm font-semibold text-gray-600 dark:text-gray-300">Lot ID</th>
                <th className="p-4 text-sm font-semibold text-gray-600 dark:text-gray-300">Species</th>
                <th className="p-4 text-sm font-semibold text-gray-600 dark:text-gray-300">Current Location</th>
                <th className="p-4 text-sm font-semibold text-gray-600 dark:text-gray-300">Status</th>
                <th className="p-4 text-sm font-semibold text-gray-600 dark:text-gray-300">Last Updated</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200/80 dark:divide-gray-700/50">
              {lots.map((lot) => {
                const latestMovement = latestMovements.get(lot.id);
                return (
                    <tr
                      key={lot.id}
                      onClick={() => onSelectLot(lot)}
                      className={`cursor-pointer hover:bg-primary/5 dark:hover:bg-primary/10 ${selectedLot?.id === lot.id ? 'bg-primary/10 dark:bg-primary/20' : ''}`}
                    >
                      <td className="p-4 whitespace-nowrap text-sm font-medium text-primary">{lot.lotId}</td>
                      <td className="p-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">{lot.species}</td>
                      <td className="p-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">{latestMovement ? latestMovement.location : 'N/A'}</td>
                      <td className="p-4 whitespace-nowrap">
                        <span className={`px-2.5 py-1 text-xs font-medium rounded-full ${
                          lot.status === 'Available' ? 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-300' :
                      lot.status === 'In-Transit' ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300' :
                      lot.status === 'Processed' ? 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-300' :
                      'bg-orange-100 dark:bg-orange-900/50 text-orange-800 dark:text-orange-300'
                    }`}>{lot.status}</span>
                  </td>
                  <td className="p-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">{latestMovement ? new Date(latestMovement.timestamp.seconds * 1000).toLocaleString() : 'N/A'}</td>
                </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </div>
    </>
  );
};
import React, { useState, useEffect, useMemo } from 'react';
import { Lot, LotMovement } from '../../types';
import { onLotsUpdate, onAllMovementsUpdate } from '../../firebase/lotService';
import { LotList } from './LotList';
import { LotDetails } from './LotDetails';
import { AddLotModal } from './AddLotModal';

export const LotTrackingView: React.FC = () => {
  const [lots, setLots] = useState<Lot[]>([]);
  const [movements, setMovements] = useState<LotMovement[]>([]);
  const [selectedLot, setSelectedLot] = useState<Lot | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    const unsubLots = onLotsUpdate(setLots);
    const unsubMovements = onAllMovementsUpdate(setMovements);
    return () => {
        unsubLots();
        unsubMovements();
    };
  }, []);

  useEffect(() => {
    // Automatically select the first lot in the list if none is selected
    if (!selectedLot && lots.length > 0) {
      setSelectedLot(lots[0]);
    }
  }, [lots, selectedLot]);

  const filteredLots = useMemo(() => {
    if (!searchQuery) {
      return lots;
    }
    return lots.filter(lot =>
      lot.lotId.toLowerCase().includes(searchQuery.toLowerCase()) ||
      lot.species.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [lots, searchQuery]);

  return (
    <div className="flex flex-col gap-6">
        <div className="flex justify-between items-start gap-4">
            <div className="flex flex-col gap-2">
                <h1 className="text-gray-900 dark:text-white text-3xl font-bold leading-tight tracking-tight">Lot Movement Tracking</h1>
                <p className="text-gray-500 dark:text-gray-400 text-base font-normal leading-normal">Monitor and manage the real-time location and history of seafood lots.</p>
            </div>
                <button
                    onClick={() => setIsModalOpen(true)}
                    className="flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-primary text-white text-sm font-bold leading-normal tracking-wide shadow-sm hover:bg-primary/90">
                    <span className="truncate">Track New Lot</span>
                </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 flex-1">
                <div className="lg:col-span-2 flex flex-col gap-4">
                    <LotList
                        lots={filteredLots}
                        movements={movements}
                        selectedLot={selectedLot}
                        onSelectLot={setSelectedLot}
                        searchQuery={searchQuery}
                        onSearchChange={setSearchQuery}
                    />
                </div>

                <div className="lg:col-span-1 flex flex-col gap-6">
                    {selectedLot ? (
                        <LotDetails lot={selectedLot} />
                    ) : (
                        <div className="rounded-xl border border-gray-200/80 dark:border-gray-700/50 bg-white dark:bg-[#152329] p-5 text-center">
                            <p className="text-gray-500 dark:text-gray-400">Select a lot to see details.</p>
                        </div>
                    )}
                </div>
            </div>
        {isModalOpen && (
            <AddLotModal onClose={() => setIsModalOpen(false)} />
        )}
    </div>
  );
};
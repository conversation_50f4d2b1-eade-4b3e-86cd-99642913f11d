import React, { useState } from 'react';
import { addLotMovement } from '../../firebase/lotService';
import { LotMovement, LotMovementType } from '../../types';

interface AddMovementModalProps {
  lotId: string;
  onClose: () => void;
}

export const AddMovementModal: React.FC<AddMovementModalProps> = ({ lotId, onClose }) => {
  const [description, setDescription] = useState('');
  const [location, setLocation] = useState('');
  const [type, setType] = useState<LotMovementType>('Arrival');
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!description || !location) {
      setError('Please fill out all fields.');
      return;
    }

    const newMovement: Omit<LotMovement, 'id' | 'timestamp'> = {
      lotId,
      description,
      location,
      type,
    };

    try {
      await addLotMovement(newMovement);
      onClose();
    } catch (err) {
      setError('Failed to add movement. Please try again.');
      console.error(err);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-[#152329] rounded-xl shadow-lg p-6 sm:p-8 max-w-lg w-full">
        <div className="flex justify-between items-center mb-6 pb-4 border-b border-gray-200/80 dark:border-gray-700/50">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">Add New Movement</h2>
          <button onClick={onClose} className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700/50">
            <span className="material-symbols-outlined text-gray-600 dark:text-gray-400">close</span>
          </button>
        </div>
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
            <div className="sm:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
              <input type="text" value={description} onChange={e => setDescription(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-transparent" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Location</label>
              <input type="text" value={location} onChange={e => setLocation(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-transparent" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Movement Type</label>
              <select value={type} onChange={e => setType(e.target.value as LotMovementType)} className="mt-1 w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-transparent">
                <option value="Arrival">Arrival</option>
                <option value="Departure">Departure</option>
                <option value="Processing">Processing</option>
                <option value="Other">Other</option>
              </select>
            </div>
          </div>
          {error && <p className="text-red-500 text-sm mb-4">{error}</p>}
          <div className="flex justify-end gap-4">
            <button type="button" onClick={onClose} className="bg-gray-200/50 dark:bg-gray-700/30 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg hover:bg-gray-300/50 dark:hover:bg-gray-600/30">Cancel</button>
            <button type="submit" className="bg-primary text-white px-4 py-2 rounded-lg shadow hover:bg-primary/90">Save Movement</button>
          </div>
        </form>
      </div>
    </div>
  );
};
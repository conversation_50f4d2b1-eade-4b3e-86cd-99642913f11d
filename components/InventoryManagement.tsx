import React, { useMemo, useState } from 'react';
import { HACCPEvent, Species, Vendor } from '../types';
import { AddProductModal } from './AddProductModal';

interface InventoryManagementProps {
  events: HACCPEvent[];
  species: Species[];
  vendors: Vendor[];
}

type InventoryItem = {
  id: string;
  name: string;
  category?: 'Fish' | 'Shellfish' | 'Other';
  stock: number;
  unitPrice?: number;
  status: 'In Stock' | 'Low Stock' | 'Out of Stock';
  supplier?: string;
  dateAdded?: string;
  imageUrl?: string;
};

const InventoryManagement: React.FC<InventoryManagementProps> = ({ events, species, vendors }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const itemsPerPage = 10;

  const inventoryData = useMemo<InventoryItem[]>(() => {
    const inventoryMap: { [productName: string]: InventoryItem } = {};

    // Initialize with all species to ensure products with 0 stock are shown
    species.forEach(s => {
      inventoryMap[s.name] = {
        id: s.id,
        name: s.name,
        category: s.category,
        stock: 0,
        status: 'Out of Stock',
        imageUrl: s.imageUrl,
      };
    });

    // Process events to calculate stock and other details
    [...events]
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .forEach(event => {
        if (!event.product || !inventoryMap[event.product]) return;

        const item = inventoryMap[event.product];
        const quantity = event.quantity || 0;

        switch (event.eventType) {
          case 'receiving':
            item.stock += quantity;
            item.supplier = event.supplier;
            item.unitPrice = event.unitPrice;
            item.dateAdded = event.date;
            break;
          case 'sales':
          case 'disposal':
            item.stock -= quantity;
            break;
        }
      });

    // Set status based on final stock count
    Object.values(inventoryMap).forEach(item => {
      if (item.stock <= 0) {
        item.status = 'Out of Stock';
        item.stock = 0; // Ensure stock is not negative
      } else if (item.stock < 50) {
        item.status = 'Low Stock';
      } else {
        item.status = 'In Stock';
      }
    });

    return Object.values(inventoryMap);
  }, [events, species]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return inventoryData.slice(startIndex, startIndex + itemsPerPage);
  }, [inventoryData, currentPage]);

  const totalPages = Math.ceil(inventoryData.length / itemsPerPage);

  const handleExport = () => {
    const headers = ["Product Name", "Category", "Stock", "Unit Price", "Status", "Supplier", "Date Added"];
    const csvContent = [
      headers.join(','),
      ...inventoryData.map(item => [
        item.name,
        item.category || 'N/A',
        item.stock.toFixed(2),
        item.unitPrice?.toFixed(2) || 'N/A',
        item.status,
        item.supplier || 'N/A',
        item.dateAdded || 'N/A'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.href) {
      URL.revokeObjectURL(link.href);
    }
    link.href = URL.createObjectURL(blob);
    link.download = 'inventory_data.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleAddProduct = (newSpecies: Partial<Species>) => {
    // Here you would typically call a function passed via props to update the database
    console.log("Adding new product:", newSpecies);
  };

  const getStatusChip = (status: InventoryItem['status']) => {
    switch (status) {
      case 'In Stock':
        return <span className="inline-flex items-center gap-1.5 rounded-full bg-success/20 px-3 py-1 text-xs font-medium text-success"><span className="size-1.5 rounded-full bg-success"></span> In Stock</span>;
      case 'Low Stock':
        return <span className="inline-flex items-center gap-1.5 rounded-full bg-warning/20 px-3 py-1 text-xs font-medium text-amber-700 dark:text-warning"><span className="size-1.5 rounded-full bg-warning"></span> Low Stock</span>;
      case 'Out of Stock':
        return <span className="inline-flex items-center gap-1.5 rounded-full bg-danger/20 px-3 py-1 text-xs font-medium text-danger"><span className="size-1.5 rounded-full bg-danger"></span> Out of Stock</span>;
    }
  };

  return (
    <div className="layout-content-container flex flex-col w-full flex-1">
      <div className="flex flex-wrap justify-between items-center gap-4 p-4">
        <p className="text-slate-900 dark:text-white text-4xl font-black leading-tight tracking-[-0.033em] min-w-72">Inventory Management</p>
        <div className="flex items-center gap-3">
          <button onClick={handleExport} className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-secondary/20 text-secondary text-sm font-bold leading-normal tracking-[0.015em] gap-2">
            <span className="material-symbols-outlined text-base">file_download</span>
            <span className="truncate">Export Data</span>
          </button>
          <button onClick={() => setIsAddModalOpen(true)} className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-primary text-white text-sm font-bold leading-normal tracking-[0.015em] gap-2">
            <span className="material-symbols-outlined text-base">add</span>
            <span className="truncate">Add New Product</span>
          </button>
        </div>
      </div>
      <div className="flex gap-3 p-4 bg-card-light dark:bg-card-dark rounded-xl border border-border-light dark:border-border-dark mt-4">
        <button className="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-lg bg-background-light dark:bg-background-dark pl-4 pr-2 border border-border-light dark:border-border-dark">
          <p className="text-slate-800 dark:text-slate-200 text-sm font-medium leading-normal">Product Type</p>
          <span className="material-symbols-outlined text-slate-800 dark:text-slate-200 !text-xl">expand_more</span>
        </button>
        <button className="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-lg bg-background-light dark:bg-background-dark pl-4 pr-2 border border-border-light dark:border-border-dark">
          <p className="text-slate-800 dark:text-slate-200 text-sm font-medium leading-normal">Status</p>
          <span className="material-symbols-outlined text-slate-800 dark:text-slate-200 !text-xl">expand_more</span>
        </button>
        <button className="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-lg bg-background-light dark:bg-background-dark pl-4 pr-2 border border-border-light dark:border-border-dark">
          <p className="text-slate-800 dark:text-slate-200 text-sm font-medium leading-normal">Supplier</p>
          <span className="material-symbols-outlined text-slate-800 dark:text-slate-200 !text-xl">expand_more</span>
        </button>
        <button className="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-lg bg-background-light dark:bg-background-dark pl-4 pr-2 border border-border-light dark:border-border-dark">
          <p className="text-slate-800 dark:text-slate-200 text-sm font-medium leading-normal">Origin</p>
          <span className="material-symbols-outlined text-slate-800 dark:text-slate-200 !text-xl">expand_more</span>
        </button>
        <button className="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-lg bg-primary/20 text-primary pl-3 pr-3 border border-primary/30 ml-auto">
          <span className="material-symbols-outlined text-primary !text-xl">filter_list</span>
          <p className="text-sm font-medium leading-normal">Filters</p>
        </button>
      </div>
      <div className="mt-4 px-0 py-3">
        <div className="flex overflow-hidden rounded-xl border border-border-light dark:border-border-dark bg-card-light dark:bg-card-dark">
          <table className="w-full text-left">
            <thead className="bg-background-light dark:bg-background-dark">
              <tr>
                <th className="px-4 py-3 text-left w-12">
                  <input className="h-4 w-4 rounded border-neutral-text-light bg-transparent text-primary checked:bg-primary checked:border-primary focus:ring-0 focus:ring-offset-0" type="checkbox"/>
                </th>
                <th className="px-4 py-3 text-left text-slate-900 dark:text-white w-16 text-sm font-medium"></th>
                <th className="px-4 py-3 text-left text-slate-900 dark:text-white text-sm font-medium">Product Name</th>
                <th className="px-4 py-3 text-left text-slate-900 dark:text-white text-sm font-medium">Category</th>
                <th className="px-4 py-3 text-left text-slate-900 dark:text-white text-sm font-medium">Stock</th>
                <th className="px-4 py-3 text-left text-slate-900 dark:text-white text-sm font-medium">Unit Price</th>
                <th className="px-4 py-3 text-left text-slate-900 dark:text-white text-sm font-medium">Status</th>
                <th className="px-4 py-3 text-left text-slate-900 dark:text-white text-sm font-medium">Supplier</th>
                <th className="px-4 py-3 text-left text-slate-900 dark:text-white text-sm font-medium">Date Added</th>
                <th className="px-4 py-3 text-left text-slate-900 dark:text-white text-sm font-medium text-center">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border-light dark:divide-border-dark">
              {paginatedData.map(item => (
                <tr key={item.id} className="hover:bg-background-light dark:hover:bg-background-dark/50">
                  <td className="h-[72px] px-4 py-2 text-center">
                    <input className="h-4 w-4 rounded border-neutral-text-light bg-transparent text-primary checked:bg-primary checked:border-primary focus:ring-0 focus:ring-offset-0" type="checkbox" />
                  </td>
                  <td className="h-[72px] px-4 py-2">
                    <div className="bg-center bg-no-repeat aspect-square bg-cover rounded-lg w-10 h-10" style={{ backgroundImage: `url(${item.imageUrl || 'https://via.placeholder.com/150'})` }}></div>
                  </td>
                  <td className="h-[72px] px-4 py-2 text-slate-800 dark:text-slate-200 text-sm font-medium">{item.name}</td>
                  <td className="h-[72px] px-4 py-2 text-neutral-text-light dark:text-neutral-text-dark text-sm">{item.category || 'N/A'}</td>
                  <td className="h-[72px] px-4 py-2 text-neutral-text-light dark:text-neutral-text-dark text-sm">{item.stock.toFixed(2)} kg</td>
                  <td className="h-[72px] px-4 py-2 text-neutral-text-light dark:text-neutral-text-dark text-sm">${item.unitPrice?.toFixed(2) || 'N/A'}</td>
                  <td className="h-[72px] px-4 py-2">{getStatusChip(item.status)}</td>
                  <td className="h-[72px] px-4 py-2 text-neutral-text-light dark:text-neutral-text-dark text-sm">{item.supplier || 'N/A'}</td>
                  <td className="h-[72px] px-4 py-2 text-neutral-text-light dark:text-neutral-text-dark text-sm">{item.dateAdded || 'N/A'}</td>
                  <td className="h-[72px] px-4 py-2 text-sm font-normal leading-normal text-center">
                    <button className="p-1 text-neutral-text-light dark:text-neutral-text-dark hover:text-primary dark:hover:text-primary"><span className="material-symbols-outlined !text-xl">edit</span></button>
                    <button className="p-1 text-neutral-text-light dark:text-neutral-text-dark hover:text-primary dark:hover:text-primary"><span className="material-symbols-outlined !text-xl">visibility</span></button>
                    <button className="p-1 text-neutral-text-light dark:text-neutral-text-dark hover:text-danger dark:hover:text-danger"><span className="material-symbols-outlined !text-xl">archive</span></button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      <div className="flex items-center justify-center p-4 mt-4">
        <button onClick={() => setCurrentPage(p => Math.max(1, p - 1))} disabled={currentPage === 1} className="flex size-10 items-center justify-center text-slate-800 dark:text-slate-200 hover:text-primary dark:hover:text-primary disabled:opacity-50">
          <span className="material-symbols-outlined !text-xl">chevron_left</span>
        </button>
        {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
          <button key={page} onClick={() => setCurrentPage(page)} className={`text-sm font-normal leading-normal flex size-10 items-center justify-center rounded-full ${currentPage === page ? 'bg-primary text-white font-bold' : 'text-slate-800 dark:text-slate-200 hover:bg-primary/20'}`}>
            {page}
          </button>
        ))}
        <button onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))} disabled={currentPage === totalPages} className="flex size-10 items-center justify-center text-slate-800 dark:text-slate-200 hover:text-primary dark:hover:text-primary disabled:opacity-50">
          <span className="material-symbols-outlined !text-xl">chevron_right</span>
        </button>
      </div>
      {isAddModalOpen && (
        <AddProductModal
          onClose={() => setIsAddModalOpen(false)}
          onSave={handleAddProduct}
        />
      )}
    </div>
  );
};

export default InventoryManagement;
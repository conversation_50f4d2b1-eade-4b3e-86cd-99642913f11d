

import React from 'react';
import { Location } from '../types';
import { XIcon, CubeIcon } from './IconComponents';

interface FreezerLayoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  location: Location;
  inventory: { [product: string]: number };
}

export const FreezerLayoutModal: React.FC<FreezerLayoutModalProps> = ({ isOpen, onClose, location, inventory }) => {
  if (!isOpen) return null;

  const inventoryItems = Object.entries(inventory).filter(([, qty]) => (qty as number) > 0.01);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4" onClick={onClose}>
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-4xl max-h-[80vh] flex flex-col" onClick={e => e.stopPropagation()}>
        <header className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">Layout for: {location.name}</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <XIcon className="h-6 w-6" />
          </button>
        </header>
        <main className="p-6 overflow-y-auto">
          {location.description && <p className="mb-6 text-gray-600">{location.description}</p>}
          {inventoryItems.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {inventoryItems.map(([product, quantity]) => (
                <div key={product} className="bg-gray-50 border border-gray-200 rounded-lg p-4 flex flex-col items-center justify-center text-center h-40">
                  <CubeIcon className="h-10 w-10 text-blue-500 mb-2 flex-shrink-0" />
                  <div className="flex flex-col justify-center flex-grow">
                    <h3 className="font-semibold text-gray-800 break-words">{product}</h3>
                    <p className="text-xl font-bold text-blue-600">{(quantity as number).toFixed(2)} lbs</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center bg-gray-50 p-12 rounded-lg">
                <p className="text-gray-500">This location is currently empty.</p>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};
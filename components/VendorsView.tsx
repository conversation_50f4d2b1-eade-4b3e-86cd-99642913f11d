import React, { useState, useEffect } from 'react';
import { db } from '../firebase/config';
import { collection, onSnapshot, query, orderBy, addDoc, updateDoc, doc, serverTimestamp, QuerySnapshot, DocumentData } from 'firebase/firestore';
import { Vendor } from '../types';
import { BuildingStorefrontIcon, PlusIcon, PhotoIcon } from './IconComponents';
import { uploadImage } from '../utils/imageUtils';
import { docToPlainObject } from '../utils/firestoreUtils';

export const VendorsView: React.FC = () => {
    const [vendors, setVendors] = useState<Vendor[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isFormOpen, setIsFormOpen] = useState(false);
    
    // Form state
    const [editingId, setEditingId] = useState<string | null>(null);
    const [name, setName] = useState('');
    const [contactPerson, setContactPerson] = useState('');
    const [phone, setPhone] = useState('');
    const [email, setEmail] = useState('');
    const [address, setAddress] = useState('');
    const [notes, setNotes] = useState('');
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const [productsCarried, setProductsCarried] = useState('');
    const [sourcingLeadTimeDays, setSourcingLeadTimeDays] = useState('');

    useEffect(() => {
        try {
            const q = query(collection(db, "vendors"), orderBy("name"));
            const unsubscribe = onSnapshot(q, (querySnapshot: QuerySnapshot<DocumentData>) => {
                const vendorsList: Vendor[] = querySnapshot.docs.map(doc => docToPlainObject<Vendor>(doc));
                setVendors(vendorsList);
                setLoading(false);
            }, (err) => {
                console.error(err);
                setError("Failed to fetch vendors.");
                setLoading(false);
            });
            return () => unsubscribe();
        } catch(e) {
            console.error(e);
            setError("Failed to initialize Firebase for vendors.");
            setLoading(false);
        }
    }, []);

    const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setImageFile(file);
            const previewUrl = URL.createObjectURL(file);
            setImagePreview(previewUrl);
        }
    };

    const resetForm = () => {
        setEditingId(null);
        setName('');
        setContactPerson('');
        setPhone('');
        setEmail('');
        setAddress('');
        setNotes('');
        setImageFile(null);
        setImagePreview(null);
        setProductsCarried('');
        setSourcingLeadTimeDays('');
        setIsFormOpen(false);
    };

    const handleEdit = (vendor: Vendor) => {
        setEditingId(vendor.id);
        setName(vendor.name);
        setContactPerson(vendor.contactPerson || '');
        setPhone(vendor.phone || '');
        setEmail(vendor.email || '');
        setAddress(vendor.address || '');
        setNotes(vendor.notes || '');
        setImagePreview(vendor.imageUrl || null);
        setProductsCarried(vendor.productsCarried?.join('\n') || '');
        setSourcingLeadTimeDays(vendor.sourcingLeadTimeDays?.toString() || '');
        setImageFile(null);
        setIsFormOpen(true);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!name) {
            alert("Vendor name is required.");
            return;
        }
        
        const productsArray = productsCarried.split('\n').map(s => s.trim()).filter(Boolean);

        const vendorData: Partial<Vendor> = { 
            name, 
            contactPerson, 
            phone, 
            email, 
            address, 
            notes, 
            productsCarried: productsArray, 
            sourcingLeadTimeDays: parseInt(sourcingLeadTimeDays, 10) || undefined 
        };

        if (imageFile) {
            try {
                const imageUrl = await uploadImage(imageFile, `vendors/${Date.now()}_${imageFile.name}`);
                vendorData.imageUrl = imageUrl;
            } catch (error) {
                alert("Failed to upload image.");
                return;
            }
        } else if (editingId && imagePreview) {
            vendorData.imageUrl = imagePreview;
        }

        try {
            if (editingId) {
                const vendorRef = doc(db, "vendors", editingId);
                await updateDoc(vendorRef, vendorData);
            } else {
                await addDoc(collection(db, "vendors"), vendorData);
            }
            resetForm();
        } catch (error) {
            console.error("Error saving vendor: ", error);
            alert("Failed to save vendor.");
        }
    };

    return (
        <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 w-full space-y-6">
            <header className="flex items-center justify-between pb-4 border-b border-gray-200">
                <div className="flex items-center">
                    <div className="bg-indigo-100 text-indigo-600 p-2 rounded-lg mr-4">
                        <BuildingStorefrontIcon className="h-6 w-6" />
                    </div>
                    <h1 className="text-2xl font-bold text-gray-800">Vendor Management</h1>
                </div>
                <button onClick={() => { resetForm(); setIsFormOpen(true); }} className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-blue-700">
                    <PlusIcon className="h-5 w-5 mr-2" />
                    Add Vendor
                </button>
            </header>

            {isFormOpen && (
                 <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                    <h2 className="text-xl font-semibold mb-4 text-gray-700">{editingId ? 'Edit Vendor' : 'Add New Vendor'}</h2>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label htmlFor="name" className="block text-sm font-medium text-gray-700">Vendor Name</label>
                                <input type="text" id="name" value={name} onChange={e => setName(e.target.value)} required className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                            </div>
                            <div>
                                <label htmlFor="contactPerson" className="block text-sm font-medium text-gray-700">Contact Person</label>
                                <input type="text" id="contactPerson" value={contactPerson} onChange={e => setContactPerson(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                            </div>
                            <div>
                                <label htmlFor="phone" className="block text-sm font-medium text-gray-700">Phone</label>
                                <input type="tel" id="phone" value={phone} onChange={e => setPhone(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                            </div>
                             <div>
                                <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email</label>
                                <input type="email" id="email" value={email} onChange={e => setEmail(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                            </div>
                             <div className="md:col-span-2">
                                <label htmlFor="address" className="block text-sm font-medium text-gray-700">Address</label>
                                <textarea id="address" value={address} onChange={e => setAddress(e.target.value)} rows={2} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"></textarea>
                            </div>
                            <div>
                                <label htmlFor="productsCarried" className="block text-sm font-medium text-gray-700">Products Carried (one per line)</label>
                                <textarea id="productsCarried" value={productsCarried} onChange={e => setProductsCarried(e.target.value)} rows={3} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"></textarea>
                            </div>
                            <div>
                                <label htmlFor="sourcingLeadTimeDays" className="block text-sm font-medium text-gray-700">Sourcing Lead Time (Days)</label>
                                <input type="number" id="sourcingLeadTimeDays" value={sourcingLeadTimeDays} onChange={e => setSourcingLeadTimeDays(e.target.value)} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"/>
                            </div>
                             <div className="md:col-span-2">
                                <label htmlFor="notes" className="block text-sm font-medium text-gray-700">Notes</label>
                                <textarea id="notes" value={notes} onChange={e => setNotes(e.target.value)} rows={3} className="mt-1 w-full p-2 border border-gray-300 rounded-md shadow-sm"></textarea>
                            </div>
                            <div className="md:col-span-2">
                                <label className="block text-sm font-medium text-gray-700">Vendor Image</label>
                                <div className="mt-2 flex items-center space-x-4">
                                     <div className="shrink-0">
                                        {imagePreview ? (
                                            <img className="h-16 w-16 object-cover rounded-md" src={imagePreview} alt="Vendor" />
                                        ) : (
                                            <div className="h-16 w-16 bg-gray-200 rounded-md flex items-center justify-center">
                                                <PhotoIcon className="h-8 w-8 text-gray-400" />
                                            </div>
                                        )}
                                    </div>
                                    <input type="file" onChange={handleImageSelect} accept="image/*" className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"/>
                                </div>
                            </div>
                        </div>
                        <div className="flex justify-end space-x-3">
                            <button type="button" onClick={resetForm} className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">Cancel</button>
                            <button type="submit" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">{editingId ? 'Update Vendor' : 'Save Vendor'}</button>
                        </div>
                    </form>
                </div>
            )}

            {loading && <p>Loading vendors...</p>}
            {error && <p className="text-red-500">{error}</p>}
            {!loading && !error && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {vendors.length === 0 ? <p className="text-center text-gray-500 py-4 md:col-span-2">No vendors added yet.</p> :
                        vendors.map(vendor => (
                            <div key={vendor.id} className="p-4 border rounded-lg flex flex-col justify-between bg-white hover:bg-gray-50">
                                <div className="flex items-start space-x-4">
                                    {vendor.imageUrl && (
                                        <img src={vendor.imageUrl} alt={vendor.name} className="h-16 w-16 rounded-lg object-cover" />
                                    )}
                                    <div className="flex-grow">
                                        <h3 className="font-semibold text-lg text-gray-800">{vendor.name}</h3>
                                        {vendor.contactPerson && <p className="text-sm text-gray-600">{vendor.contactPerson} - {vendor.phone}</p>}
                                        {vendor.sourcingLeadTimeDays && <p className="text-xs text-gray-500 mt-1">Lead Time: <span className="font-bold">{vendor.sourcingLeadTimeDays} days</span></p>}
                                    </div>
                                </div>
                                 <div className="mt-2 text-sm text-gray-700">
                                    <h4 className="font-semibold">Products:</h4>
                                    <p className="text-gray-600 text-xs">{vendor.productsCarried?.join(', ') || 'N/A'}</p>
                                </div>
                                <div className="mt-2 pt-2 border-t flex justify-end">
                                    <button onClick={() => handleEdit(vendor)} className="text-blue-600 hover:text-blue-800 font-medium">Edit</button>
                                </div>
                            </div>
                        ))
                    }
                </div>
            )}
        </div>
    );
};
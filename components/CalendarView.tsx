import React, { useState, useMemo } from 'react';
import { HACCPEvent } from '../types';
import { CalendarIcon, PackageIcon, ShoppingCartIcon, TrashIcon, ClipboardIcon, XIcon, ClockIcon, SparklesIcon, ThermometerIcon, ArchiveBoxIcon, PlusIcon, UserCircleIcon, TruckIcon } from './IconComponents';

interface CalendarViewProps {
    events: HACCPEvent[];
    onAddNewEvent: (date: string) => void;
}

const EventTypeDisplay = ({ eventType }: { eventType: string }) => {
    const typeMap: { [key: string]: { icon: React.ReactNode; label: string; color: string } } = {
        receiving: { icon: <PackageIcon />, label: 'Receiving', color: 'blue' },
        sales: { icon: <ShoppingCartIcon />, label: 'Sales', color: 'green' },
        disposal: { icon: <TrashIcon />, label: 'Disposal', color: 'red' },
        're-sealing': { icon: <ClipboardIcon />, label: 'Re-sealing', color: 'purple' },
        relocation: { icon: <TruckIcon />, label: 'Relocation', color: 'indigo' },
        sanitation: { icon: <SparklesIcon />, label: 'Sanitation', color: 'teal' },
        'thermometer-calibration': { icon: <ThermometerIcon />, label: 'Calibration', color: 'orange' },
        inventory: { icon: <ArchiveBoxIcon />, label: 'Inventory', color: 'yellow' },
        'employee-training': { icon: <UserCircleIcon />, label: 'Training', color: 'cyan' },
    };
    const { icon, label, color } = typeMap[eventType] || { icon: null, label: eventType, color: 'gray' };

    return (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${color}-100 text-${color}-800`}>
            <div className={`w-4 h-4 mr-1.5 text-${color}-600`}>{icon}</div>
            {label}
        </span>
    )
}

const EventModal: React.FC<{ date: string, events: HACCPEvent[], onClose: () => void }> = ({ date, events, onClose }) => {
    const formattedDate = new Date(date + 'T00:00:00').toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
    });

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" onClick={onClose}>
            <div className="bg-white rounded-2xl shadow-xl w-full max-w-2xl max-h-[80vh] flex flex-col" onClick={e => e.stopPropagation()}>
                <header className="flex items-center justify-between p-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-800">{formattedDate}</h2>
                    <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
                        <XIcon className="h-6 w-6" />
                    </button>
                </header>
                <div className="p-6 overflow-y-auto space-y-4">
                    {events.length > 0 ? events.sort((a,b) => a.time.localeCompare(b.time)).map(event => (
                        <div key={event.id} className="p-4 border border-gray-200 rounded-lg bg-gray-50/50">
                            <div className="flex justify-between items-start">
                                <EventTypeDisplay eventType={event.eventType} />
                                <div className="flex items-center text-sm text-gray-500">
                                    <ClockIcon className="w-4 h-4 mr-1" />
                                    <span>{event.time}</span>
                                </div>
                            </div>
                            <div className="mt-2 flex space-x-4">
                                {event.imageUrl && (
                                    <div className="flex-shrink-0">
                                        <img src={event.imageUrl} alt={event.imageDescription || "Event image"} className="h-24 w-24 rounded-md object-cover" />
                                    </div>
                                )}
                                <div className="flex-grow">
                                    {(event.eventType === 'receiving' || event.eventType === 'sales' || event.eventType === 'disposal' || event.eventType === 're-sealing' || event.eventType === 'inventory' || event.eventType === 'relocation') && (
                                        <>
                                            <p className="font-semibold text-gray-900">{[event.product, event.productForm].filter(Boolean).join(' - ')}</p>
                                            <div className="text-sm text-gray-600 grid grid-cols-2 gap-x-4 mt-1">
                                                {event.quantity && <p><span className="font-medium">Quantity:</span> {event.quantity.toFixed(2)} {event.unit}</p>}
                                                {event.batchNumber && <p><span className="font-medium">Batch:</span> <span className="font-mono text-xs">{event.batchNumber}</span></p>}
                                                {event.supplier && <p><span className="font-medium">Supplier:</span> {event.supplier}</p>}
                                                {event.temperature && <p><span className="font-medium">Temp:</span> {event.temperature}°F</p>}
                                                {event.fromLocation && <p><span className="font-medium">From:</span> {event.fromLocation}</p>}
                                                {event.location && <p><span className="font-medium">{event.eventType === 'relocation' ? 'To:' : 'Location:'}</span> {event.location}</p>}
                                            </div>
                                        </>
                                    )}
                                     {event.eventType === 'employee-training' && (
                                        <div className="text-sm text-gray-600 grid grid-cols-2 gap-x-4 mt-1">
                                            <p className="col-span-2"><span className="font-medium">Employee:</span> {event.employeeName}</p>
                                            <p className="col-span-2"><span className="font-medium">Topic:</span> {event.trainingTopic}</p>
                                        </div>
                                    )}
                                    {event.eventType === 'sanitation' && (
                                        <div className="text-sm text-gray-600 grid grid-cols-2 gap-x-4 mt-1">
                                            <p className="col-span-2"><span className="font-medium">Area Cleaned:</span> {event.areaCleaned}</p>
                                            <p className="col-span-2"><span className="font-medium">Sanitizer Used:</span> {event.sanitizerUsed}</p>
                                        </div>
                                    )}
                                     {event.eventType === 'thermometer-calibration' && (
                                        <div className="text-sm text-gray-600 grid grid-cols-2 gap-x-4 mt-1">
                                            <p><span className="font-medium">Thermometer ID:</span> {event.thermometerId}</p>
                                            <p><span className="font-medium">Result:</span> <span className={`font-bold ${event.result === 'pass' ? 'text-green-600' : 'text-red-600'}`}>{event.result?.toUpperCase()}</span></p>
                                            <p className="col-span-2"><span className="font-medium">Method:</span> {event.calibrationMethod}</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                            {event.imageDescription && <p className="text-xs text-gray-500 mt-2 pt-2 border-t border-gray-200">AI Analysis: {event.imageDescription}</p>}
                            {event.correctiveAction && <p className="text-xs text-gray-500 mt-2 pt-2 border-t border-gray-200">Corrective Action: {event.correctiveAction}</p>}
                            {event.notes && <p className="text-xs text-gray-500 mt-2 pt-2 border-t border-gray-200">Notes: {event.notes}</p>}
                        </div>
                    )) : <p>No events found for this date.</p>}
                </div>
            </div>
        </div>
    );
};


export const CalendarView: React.FC<CalendarViewProps> = ({ events, onAddNewEvent }) => {
    const [currentDate, setCurrentDate] = useState(new Date());
    const [selectedDate, setSelectedDate] = useState<string | null>(null);

    const eventsByDate = useMemo(() => {
        const map = new Map<string, HACCPEvent[]>();
        events.forEach(event => {
            const dateStr = event.date; // Assuming date is already 'YYYY-MM-DD'
            if (!map.has(dateStr)) {
                map.set(dateStr, []);
            }
            map.get(dateStr)!.push(event);
        });
        return map;
    }, [events]);

    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const firstDayOfMonth = new Date(year, month, 1).getDay();

    const changeMonth = (delta: number) => {
        setCurrentDate(prev => {
            const newDate = new Date(prev);
            newDate.setMonth(newDate.getMonth() + delta);
            return newDate;
        });
    };

    const calendarDays = [];
    for (let i = 0; i < firstDayOfMonth; i++) {
        calendarDays.push(<div key={`empty-${i}`} className="border-r border-b border-gray-200"></div>);
    }
    for (let day = 1; day <= daysInMonth; day++) {
        const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        const dayEvents = eventsByDate.get(dateStr);
        const isToday = new Date().toDateString() === new Date(year, month, day).toDateString();

        calendarDays.push(
            <div 
                key={day} 
                className={`relative group p-2 border-r border-b border-gray-200 min-h-[5rem] sm:min-h-[6rem] transition-colors duration-200 ${dayEvents && dayEvents.length > 0 ? 'cursor-pointer hover:bg-gray-100' : ''}`}
                onClick={() => dayEvents && dayEvents.length > 0 && setSelectedDate(dateStr)}
            >
                <time className={`text-sm ${isToday ? 'bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center font-bold' : 'text-gray-600'}`}>
                    {day}
                </time>
                 <button
                    onClick={(e) => {
                        e.stopPropagation(); // Prevent opening the modal
                        onAddNewEvent(dateStr);
                    }}
                    title="Add new event for this date"
                    aria-label="Add new event for this date"
                    className="absolute top-1 right-1 p-1 rounded-full bg-blue-500 text-white opacity-0 group-hover:opacity-100 focus:opacity-100 transition-opacity z-10"
                >
                    <PlusIcon className="h-4 w-4" />
                </button>
                {dayEvents && dayEvents.length > 0 && (
                    <div className="absolute bottom-2 left-2 right-2 flex justify-center space-x-1">
                        <span className="h-2 w-2 bg-blue-500 rounded-full" title="Event"></span>
                         {dayEvents.length > 1 && <span className="h-2 w-2 bg-green-500 rounded-full" title="Multiple Events"></span>}
                         {dayEvents.length > 2 && <span className="h-2 w-2 bg-red-500 rounded-full" title="Multiple Events"></span>}
                    </div>
                )}
            </div>
        );
    }

    return (
        <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 w-full">
            <header className="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
                <div className="flex items-center">
                    <div className="bg-purple-100 text-purple-600 p-2 rounded-lg mr-4">
                        <CalendarIcon className="h-6 w-6" />
                    </div>
                    <h1 className="text-2xl font-bold text-gray-800">Event Calendar</h1>
                </div>
                 <div className="flex items-center space-x-2">
                    <button onClick={() => changeMonth(-1)} className="p-2 rounded-full hover:bg-gray-200 transition-colors duration-200" aria-label="Previous month">
                         <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path></svg>
                    </button>
                    <span className="text-lg font-semibold text-gray-700 text-center">
                        {currentDate.toLocaleString('default', { month: 'long', year: 'numeric' })}
                    </span>
                    <button onClick={() => changeMonth(1)} className="p-2 rounded-full hover:bg-gray-200 transition-colors duration-200" aria-label="Next month">
                        <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path></svg>
                    </button>
                </div>
            </header>
            
            <div className="border-t border-l border-gray-200">
                <div className="grid grid-cols-7">
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                        <div key={day} className="text-center font-semibold text-xs text-gray-500 py-2 border-r border-b border-gray-200 bg-gray-50">
                           {day}
                        </div>
                    ))}
                </div>
                <div className="grid grid-cols-7">
                    {calendarDays}
                </div>
            </div>
            
            {selectedDate && <EventModal date={selectedDate} events={eventsByDate.get(selectedDate) || []} onClose={() => setSelectedDate(null)} />}
        </div>
    );
};